{"name": "smart-resume-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/", "prepare": "husky", "lint-staged": "lint-staged"}, "dependencies": {"@alova/adapter-axios": "^2.0.16", "@alova/mock": "^2.0.17", "@tailwindcss/vite": "^4.1.10", "@vueuse/core": "^13.4.0", "alova": "^3.3.3", "autofit.js": "^3.2.8", "axios": "^1.10.0", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.10.2", "js-base64": "^3.7.7", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "nprogress": "^0.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "screenfull": "^6.0.2", "tailwindcss": "^4.1.10", "vue": "^3.5.16", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "globals": "^16.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "3.6.0", "prettier-plugin-css-order": "^2.1.2", "prettier-plugin-tailwindcss": "^0.6.11", "rollup-plugin-visualizer": "^6.0.3", "sass-embedded": "^1.87.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vite-plugin-compression2": "^2.2.0", "vite-plugin-vue-devtools": "^7.7.7"}, "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "esbuild"]}, "engines": {"node": ">=20.18.0", "pnpm": ">=10"}}