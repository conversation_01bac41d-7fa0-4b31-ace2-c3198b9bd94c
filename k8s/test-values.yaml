replicaCount: 1
imagePullSecrets:
  - name: harbor-secret
service:
  port: 80
ingress:
  enabled: true
  hosts:
    - host: smart-resume-web.test.youpin-k8s.net
      paths:
        - path: /
configMap:
  env:
    WEBAPP_ENV.K8S_APP_ENVIRONMENT: TEST
    WEBAPP_ENV.K8S_API_LOWCODE_BASE_URL: https://lowcode-plus.test.youpin-k8s.net
    WEBAPP_ENV.K8S_API_BASE_URL: https://smart-resume.test.youpin-k8s.net
    WEBAPP_ENV.K8S_PROJECT_PREFIX: f84eda81de2b29c3bdcd79d4c1c0b4e8
    WEBAPP_ENV.K8S_SPACE_ID: '1258676040982142976'
    WEBAPP_ENV.K8S_APP_GEO_JSON_BASE: https://oss.youpin-k8s.net/shared-space/geo-json/100000
    WEBAPP_ENV.K8S_KK_FILE_VIEW_URL: https://file-view.youpin-k8s.net/onlinePreview
resources:
  limits:
    cpu: 100m
    memory: 100Mi
  requests:
    cpu: 100m
    memory: 100Mi
