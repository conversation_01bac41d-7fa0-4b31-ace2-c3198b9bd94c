import js from '@eslint/js'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'
import pluginVue from 'eslint-plugin-vue'
import { defineConfig, globalIgnores } from 'eslint/config'
import globals from 'globals'
import { createRequire } from 'node:module'
const autoImportGlobals = createRequire(import.meta.url)(
  './.eslintrc-auto-import.json',
)

export default defineConfig([
  // 自定义配置对象 用于指定要检查的文件
  {
    name: 'app/files-to-lint',
    files: ['**/*.{js,mjs,jsx,vue}'],
  },
  // 全局忽略文件
  globalIgnores(['**/dist/**', '**/dist-ssr/**', '**/coverage/**']),
  // 全局变量
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...autoImportGlobals.globals,
      },
    },
  },
  // 推荐的js lint
  js.configs.recommended,
  // 采用vue推荐的lint规则
  ...pluginVue.configs['flat/recommended'],
  // 自定义覆盖规则
  {
    rules: {
      // 除了index.vue外，其他组件名必须使用多单词
      'vue/multi-word-component-names': [
        'error',
        {
          ignores: ['index', 'Header'],
        },
      ],
      // 使用组件使用 PascalCase 形式
      'vue/component-name-in-template-casing': [
        'error',
        'PascalCase',
        {
          registeredComponentsOnly: false,
        },
      ],
      'vue/singleline-html-element-content-newline': 'off',
      'vue/html-closing-bracket-newline': [
        'warn',
        {
          multiline: 'never',
        },
      ],
      'vue/html-self-closing': [
        'warn',
        {
          html: {
            void: 'any',
            normal: 'always',
            component: 'always',
          },
        },
      ],
      'vue/block-order': [
        'error',
        {
          order: ['template', 'script', 'style'],
        },
      ],
      'vue/max-attributes-per-line': [
        'error',
        {
          singleline: {
            max: 1,
          },
          multiline: {
            max: 1,
          },
        },
      ],
      'vue/prefer-true-attribute-shorthand': ['error', 'never', {}],
    },
  },
  // 跳过格式化规则 里面已经配置了eslint-config-prettier的规则
  skipFormatting,
])
