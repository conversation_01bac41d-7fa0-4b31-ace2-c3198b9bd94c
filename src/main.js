import '@/assets/styles/index.scss'
import '@/assets/styles/tailwind.css'
import 'nprogress/nprogress.css'

import { createApp } from 'vue'
import App from './App.vue'

import router from '@/router'
import pinia from '@/stores'

import directive from '@/directives/index.js'
import '@/plugins/dayjs'
import vChart from '@/plugins/v-chart'

const app = createApp(App)

app.use(router)
app.use(pinia)
app.use(directive)
app.use(vChart)

app.mount('#app')
