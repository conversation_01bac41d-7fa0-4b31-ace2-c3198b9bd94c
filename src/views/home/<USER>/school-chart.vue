<template>
  <VChart
    :option
    class="school" />
</template>

<script setup>
  import { numberSplit } from '@/utils'
  import * as echarts from 'echarts'

  const props = defineProps({
    chartData: {
      type: Array,
      default: () => [],
    },
  })

  const option = computed(() => ({
    grid: {
      top: 50,
      bottom: 0,
      left: 0,
      right: 0,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      formatter(param) {
        return `
            <div>
              <div style="font-size: 12px;color: rgba(0,0,0,0.45);margin-bottom: 4px;">学校分布</div>
              <div style="width: 134px;display: flex;justify-content: space-between;">
                <span style="font-size: 12px;color: rgba(0,0,0,0.45);">${
                  param[0].name
                }</span>
                <span style="font-weight: 600;font-size: 12px;color: rgba(0,0,0,0.88);">${numberSplit(
                  param[0].value,
                )}人</span>
              </div>
            </div>
          `
      },
    },
    xAxis: [
      {
        type: 'category',
        axisLine: {
          lineStyle: {
            color: '#C9CDD4',
          },
        },

        axisLabel: {
          color: '#86909C',
          interval: 0,
          align: 'center',
          rotate: 15,
          margin: 20,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisPointer: {
          type: 'line',
          lineStyle: {
            width: 2,
            color: '#C9CDD4',
          },
          z: -1,
          label: {
            show: false,
          },
        },
        data: props.chartData.map((item) => item.name) || [],
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: '#86909C',
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#E5E6EB',
            type: 'dashed',
          },
        },
      },
    ],
    series: [
      {
        name: '分数',
        type: 'line',
        showSymbol: false,
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(0,85,255,0.15)',
            },
            {
              offset: 1,
              color: 'rgba(0,85,255,0)',
            },
          ]),
        },
        lineStyle: {
          normal: {
            color: '#0055FF',
            width: 3,
          },
        },
        itemStyle: {
          color: '#0055FF',
        },
        emphasis: {
          scale: 3,
          itemStyle: {
            borderWidth: 3,
          },
        },
        data: props.chartData || [],
      },
    ],
  }))
</script>

<style lang="scss" scoped>
  .school {
    width: 100%;
    height: 100%;
  }
</style>
