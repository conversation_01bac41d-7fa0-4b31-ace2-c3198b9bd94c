<template>
  <VChart
    :option
    class="h-full w-full" />
</template>

<script setup>
  const props = defineProps({
    chartData: {
      type: Array,
      default: () => [],
    },
  })

  const option = computed(() => ({
    color: ['#0055FF', '#FF7E86'],
    legend: {
      top: '75%',
      left: 'center',
      icon: 'circle',
    },
    series: [
      {
        name: 'pie',
        type: 'pie',
        radius: ['40%', '55%'],
        center: ['50%', '37%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 3,
        },
        label: {
          show: true,
          formatter: '{d}%',
          color: '#606266',
        },
        data: props.chartData || [],
      },
    ],
  }))
</script>

<style lang="scss" scoped></style>
