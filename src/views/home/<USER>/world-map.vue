<template>
  <div
    v-loading="loading"
    class="chartContainer">
    <div
      v-show="mapStack.length > 1"
      class="back"
      @click="onZoomOutMap">
      <ElButton
        link
        type="primary">
        返回
      </ElButton>
    </div>
    <VChart
      ref="chartRef"
      :option="option"
      class="map"
      @click="onClickMap" />
  </div>
</template>

<script setup>
  import axios from 'axios'
  import * as echarts from 'echarts'

  import { getHomeMap } from '@/apis/home'
  import chinaJson from '@/assets/json/china.json'
  import { numberSplit } from '@/utils'
  import { geoApiUrl } from '@/utils/config.js'

  const emits = defineEmits(['mapChange'])

  echarts.registerMap('china', chinaJson)

  const chartRef = ref(null)

  const axiosInstance = axios.create({})

  const loading = ref(false)

  const mapStack = ref([
    {
      name: 'china',
      json: china<PERSON>son,
      level: '',
      code: '',
    },
  ])

  const mapData = ref([])

  onMounted(async () => {
    await getMapData()
    emits('mapChange', 'china', 1, mapData.value)
  })

  async function getMapData(code = '', type = '') {
    try {
      loading.value = true
      const {
        data: { data: res },
      } = await getHomeMap({
        code,
        type,
      })
      mapData.value = res
      option.series[0].data = res
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  function getMapApiParams(name, level, isNext = true) {
    if (isNext) {
      const levelMapData = mapData.value.find((item) => item.name === name)
      let code = levelMapData?.code
      let type = null
      if (levelMapData?.isSecond) {
        type = 1
        code += '01'
      } else {
        if (level === 2) {
          type = 0
        } else if (level === 3) {
          type = 1
        }
      }
      return {
        code,
        type,
      }
    } else {
      const code = mapStack.value[1].code
      let type = ''
      if (level === 3) {
        type = 0
      }
      return {
        code,
        type,
      }
    }
  }

  function onClickMap(params) {
    const { name } = params

    // 演示 仅湖北省和武汉市可点
    // if (name !== '湖北省' && name !== '武汉市') {
    //   return
    // }

    const {
      properties: { adcode, childrenNum, level },
    } = mapStack.value[0].json.features.find((item) => {
      return item.properties.name === name
    })

    const code = mapData.value.find((item) => item.name === name)?.code

    if (childrenNum > 0 && adcode) {
      // 获取下一级的地图 geoJson
      axiosInstance
        .get(`${geoApiUrl}/${adcode}.json`)
        .then(async (res) => {
          if (res.status === 200) {
            const mapJson = res.data
            // 地图入栈
            mapStack.value.unshift({
              name,
              json: mapJson,
              level,
              code,
            })
            echarts.registerMap(name, mapJson)
            chartRef.value.clear()
            option.series[0].map = name
            option.geo.map = name
            const params = getMapApiParams(name, mapStack.value.length)
            await getMapData(params.code, params.type)
            emits(
              'mapChange',
              name,
              mapStack.value.length,
              mapData.value,
              params,
            )
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {})
    }
  }

  // 返回上一级
  async function onZoomOutMap() {
    const parentMap = mapStack.value[1]
    chartRef.value.clear()
    option.series[0].map = parentMap.name
    option.geo.map = parentMap.name
    const params = getMapApiParams('', mapStack.value.length, false)
    await getMapData(params.code, params.type)
    mapStack.value.shift()
    emits(
      'mapChange',
      parentMap.name,
      mapStack.value.length,
      mapData.value,
      params,
    )
  }

  const option = reactive({
    geo: {
      emphasis: {
        disabled: true,
      },
      itemStyle: {
        areaColor: '#E8F3FF',
        borderWidth: 1,
        borderColor: 'rgba(196,229,255, 0.50)',
        borderType: 'solid',
      },
      map: 'china',
    },
    visualMap: {
      text: ['Max', '0'],
      max: 100,
      show: false,
      type: 'piecewise',
      pieces: [
        {
          value: 0,
          color: '#E8F3FF',
        },
        {
          min: 1,
          max: 100,
          color: '#91CBFB',
        },
        {
          min: 101,
          max: 500,
          color: '#62ACFB',
        },
        {
          min: 501,
          max: 1000,
          color: '#4E8CEC',
        },
        {
          min: 1001,
          color: '#2D5CBC',
        },
      ],
      outOfRange: {
        color: '#E8F3FF',
      },
      inRange: {
        color: ['#E8F3FF'],
      },
    },
    tooltip: {
      formatter(param) {
        return `
            <div style="display:flex;align-items:center;">
              <div style="width:6px;height:6px;border-radius:50%;background:#2D5CBC"></div>
              <div style="margin:0 20px 0 8px">${param.name}</div>
              <div>${numberSplit(param.value || 0)}人</div>
            </div>
          `
      },
    },
    series: [
      {
        type: 'map',
        map: 'china',
        center: ['50%', '50%'],
        selectedMode: false,
        // roam: true,
        geoIndex: 0,
        data: [],
      },
    ],
  })
</script>

<style lang="scss" scoped>
  .chartContainer {
    position: relative;

    .back {
      z-index: 9999;
      position: absolute;
      top: 0;
      right: 0;
    }

    .map {
      width: 975px;
      height: 542px;
    }
  }
</style>
