<template>
  <VChart
    ref="age"
    :option="option"
    class="age" />
</template>

<script setup>
  const props = defineProps({
    chartData: {
      type: Array,
      default: () => [],
    },
  })

  const age = ref()

  const option = reactive({
    color: ['#0055FF', '#FF7E86'],
    legend: {
      top: '0',
      right: '0',
      icon: 'circle',
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter(param) {
        return `
            <div>
              <div style="font-size: 12px;color: rgba(0,0,0,0.45);margin-bottom: 4px;">${
                param[0].name
              }</div>
              <div style="width: 106px;display: flex;justify-content: space-between;align-items: center;">
                <div style="display:flex;align-items: center;">
                  <span style="width: 6px;height: 6px;background: #73767A;border-radius: 50%;margin-right: 4px;"></span>
                  <span style="font-size: 12px;color: rgba(0,0,0,0.45);">总人数</span>
                </div>
                <span style="font-weight: 600;font-size: 12px;color: rgba(0,0,0,0.88);">${
                  +param[0].value + +param[1].value
                }人</span>
              </div>
              <div style="width: 106px;display: flex;justify-content: space-between;align-items: center;">
                <div style="display:flex;align-items: center;">
                  <span style="width: 6px;height: 6px;background: #0055FF;border-radius: 50%;margin-right: 4px;"></span>
                  <span style="font-size: 12px;color: rgba(0,0,0,0.45);">男</span>
                </div>
                <span style="font-weight: 600;font-size: 12px;color: rgba(0,0,0,0.88);">${
                  param[0].value
                }人</span>
              </div>
              <div style="width: 106px;display: flex;justify-content: space-between;align-items: center;">
                <div style="display:flex;align-items: center;">
                  <span style="width: 6px;height: 6px;background: #FF7E86;border-radius: 50%;margin-right: 4px;"></span>
                  <span style="font-size: 12px;color: rgba(0,0,0,0.45);">女</span>
                </div>
                <span style="font-weight: 600;font-size: 12px;color: rgba(0,0,0,0.88);">${
                  param[1].value
                }人</span>
              </div>
            </div>
          `
      },
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      top: '40px',
      bottom: '0',
      left: '0',
      right: '12px',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        color: '#86909C',
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#E5E6EB',
          type: 'dashed',
        },
      },
    },
    yAxis: {
      type: 'category',
      inverse: true,
      data: ['25岁以下', '26-35岁', '36-45岁', '46-55岁'],
      axisLine: {
        lineStyle: {
          color: '#C9CDD4',
        },
      },
      axisLabel: {
        color: '#86909C',
        interval: 0,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    series: [
      {
        name: '男',
        type: 'bar',
        stack: 'total',
        barWidth: '10px',
        emphasis: {
          focus: 'series',
        },
        data: props.chartData.find((item) => item.name === '男')?.data || [],
      },
      {
        name: '女',
        type: 'bar',
        stack: 'total',
        barWidth: '10px',
        emphasis: {
          focus: 'series',
        },
        data: props.chartData.find((item) => item.name === '女')?.data || [],
      },
    ],
  })

  watch(
    () => props.chartData,
    (nv) => {
      const series1 = nv.find((item) => item.name === '男')?.data || []
      const series2 = nv.find((item) => item.name === '女')?.data || []
      option.series[0].data = series1
      option.series[1].data = series2
    },
    {
      immediate: true,
      deep: true,
    },
  )
</script>

<style lang="scss" scoped>
  .age {
    width: 100%;
    height: 100%;
  }
</style>
