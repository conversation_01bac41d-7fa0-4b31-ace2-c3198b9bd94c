<template>
  <VChart
    ref="education"
    :option="option"
    class="education" />
</template>

<script setup>
  const props = defineProps({
    chartData: {
      type: Array,
      default: () => [],
    },
  })

  const option = reactive({
    color: ['#F59E0B'],
    tooltip: {
      trigger: 'axis',
      formatter(param) {
        return `
            <div>
              <div style="font-size: 12px;color: rgba(0,0,0,0.45);margin-bottom: 4px;">学历分布</div>
              <div style="width: 120px;display: flex;justify-content: space-between;">
                <span style="font-size: 12px;color: rgba(0,0,0,0.45);">${param[0].name}</span>
                <span style="font-weight: 600;font-size: 12px;color: rgba(0,0,0,0.88);">${param[0].value}人</span>
              </div>
            </div>
          `
      },
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      top: '12px',
      left: '0',
      right: '0',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['大专以下', '大专', '本科', '硕士', '博士'],
        axisLine: {
          lineStyle: {
            color: '#C9CDD4',
          },
        },
        axisLabel: {
          color: '#86909C',
          interval: 0,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: '#86909C',
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#E5E6EB',
            type: 'dashed',
          },
        },
      },
    ],
    series: [
      {
        name: '学历分布',
        type: 'bar',
        barWidth: '20px',
        data: props.chartData || [],
      },
    ],
  })

  watch(
    () => props.chartData,
    (nv) => {
      option.series[0].data = nv
    },
    {
      immediate: true,
    },
  )
</script>

<style lang="scss" scoped>
  .education {
    width: 100%;
    height: 100%;
  }
</style>
