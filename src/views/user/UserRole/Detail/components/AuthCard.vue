<template>
  <div class="roleAuthCard">
    <div class="roleTitle">
      <ElCheckbox
        disabled
        :model-value="result.checkAll"
        :indeterminate="result.Indeterminate">
        {{ title }}
      </ElCheckbox>
    </div>
    <ElCheckboxGroup class="authItems">
      <template v-for="(item, index) in list">
        <div
          v-if="item.parent === strKey"
          :key="index"
          class="authCard">
          <ElCheckbox
            disabled
            :value="item.label"
            :label="item.label"
            :checked="item.check" />
        </div>
      </template>
    </ElCheckboxGroup>
  </div>
</template>

<script setup>
  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => [],
    },
    strKey: {
      type: String,
      default: '',
    },
  })

  const result = computed(() => {
    const list = props.list.filter((item) => item.parent === props.strKey)
    let checkNum = 0,
      unCheckNum = 0
    list.forEach((item) => {
      if (item.check) {
        checkNum++
      } else {
        unCheckNum++
      }
    })
    return {
      Indeterminate: checkNum !== 0 && unCheckNum !== 0,
      checkAll: unCheckNum === 0,
    }
  })
</script>

<style lang="scss" scoped>
  .roleAuthCard {
    .roleTitle {
      padding: 0 16px;
      border-radius: 4px;
      background: #f2f3f5;
    }

    .authItems {
      display: flex;
      flex-wrap: wrap;
      margin-top: 12px;
      gap: 16px;

      .authCard {
        width: 317px;
        height: 80px;
        padding: 15px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
      }
    }

    & + .roleAuthCard {
      margin-top: 16px;
    }
  }
</style>
