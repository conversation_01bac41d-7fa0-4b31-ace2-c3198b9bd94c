<template>
  <div class="containerBox">
    <BackTitle title="查看角色" />
    <div class="flex-1 overflow-y-auto">
      <div class="item">
        <div class="itemTitle">基本设置</div>
        <div class="content flex items-center">
          <label>角色名称</label>
          <div class="ml-[12px] w-[306px]">
            <ElSelect
              :model-value="role"
              disabled />
          </div>
        </div>
      </div>
      <div class="item">
        <div class="itemTitle">权限设置</div>
        <div class="content h-full">
          <div class="contentCard">
            <div class="cardTitle">简历管理</div>
            <ElDivider style="margin: 12px 0" />
            <AuthCard
              title="简历解析"
              :list="checkData"
              str-key="resumeAnalysis" />
            <AuthCard
              title="人才画像"
              :list="checkData"
              str-key="personnel" />
          </div>
          <div class="contentCard">
            <div class="cardTitle">标签管理</div>
            <ElDivider style="margin: 12px 0" />
            <AuthCard
              title="标签大类管理"
              :list="checkData"
              str-key="tagCate" />
            <AuthCard
              title="标签管理"
              :list="checkData"
              str-key="tagManage" />
          </div>
          <div class="contentCard">
            <div class="cardTitle">用户管理</div>
            <ElDivider style="margin: 12px 0" />
            <AuthCard
              title="账号管理"
              :list="checkData"
              str-key="account" />
            <AuthCard
              title="角色管理"
              :list="checkData"
              str-key="role" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import BackTitle from '@/components/back-title/index.vue'
  import AuthCard from '@/views/user/UserRole/Detail/components/AuthCard.vue'

  const route = useRoute()

  const role = route.query.role

  const checkData = ref([
    {
      label: '查看任务',
      parent: 'resumeAnalysis',
      check: true,
    },
    {
      label: '新增任务',
      parent: 'resumeAnalysis',
      check: true,
    },
    {
      label: '删除任务',
      parent: 'resumeAnalysis',
      check: true,
    },
    {
      label: '筛选任务',
      parent: 'resumeAnalysis',
      check: true,
    },
    {
      label: '编辑任务',
      parent: 'resumeAnalysis',
      check: true,
    },
    {
      label: '下载原件',
      parent: 'resumeAnalysis',
      check: true,
    },
    {
      label: '启动/暂停/终止任务',
      parent: 'resumeAnalysis',
      check: true,
    },
    {
      label: '查看人才',
      parent: 'personnel',
      check: true,
    },
    {
      label: '筛选人才',
      parent: 'personnel',
      check: true,
    },
    {
      label: '查看标签大类',
      parent: 'tagCate',
      check: true,
    },
    {
      label: '查看标签',
      parent: 'tagManage',
      check: true,
    },
    {
      label: '查看账号',
      parent: 'account',
      check: true,
    },
    {
      label: '新增账号',
      parent: 'account',
      check: role === '超级管理员',
    },
    {
      label: '删除账号',
      parent: 'account',
      check: role === '超级管理员',
    },
    {
      label: '筛选账号',
      parent: 'account',
      check: true,
    },
    {
      label: '编辑账号',
      parent: 'account',
      check: role === '超级管理员',
    },
    {
      label: '重置密码',
      parent: 'account',
      check: role === '超级管理员',
    },
    {
      label: '启用/禁用账号',
      parent: 'account',
      check: role === '超级管理员',
    },
    {
      label: '查看角色',
      parent: 'role',
      check: true,
    },
  ])
</script>

<style lang="scss" scoped>
  .containerBox {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px 0 20px 20px;
    border-radius: 6px;
    background-color: #fff;

    .item {
      margin-top: 30px;
      padding-right: 10px;

      .itemTitle {
        margin-bottom: 16px;
        color: #3d3d3d;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
      }

      .content {
        .contentCard {
          .cardTitle {
            color: #3d3d3d;
            font-size: 14px;
            line-height: 24px;
          }

          & + .contentCard {
            margin-top: 24px;
          }
        }
      }
    }
  }
</style>
