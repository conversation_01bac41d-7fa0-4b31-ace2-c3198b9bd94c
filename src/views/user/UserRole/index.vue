<template>
  <div class="pageBox">
    <div class="title">角色管理</div>
    <ElTable
      border
      class="flex-1"
      :data="tableData">
      <ElTableColumn
        label="序号"
        :index="indexMethod"
        width="60"
        type="index" />
      <ElTableColumn
        label="角色"
        prop="roleName" />
      <ElTableColumn
        label="创建时间"
        prop="createTime" />
      <!-- <ElTableColumn label="关联账号数">
        <template #default="{ row }">
          <span class="text-[#409EFF]">{{ row.userNum }}</span>
        </template>
      </ElTableColumn> -->
      <ElTableColumn label="操作">
        <template #default="{ row }">
          <ElButton
            link
            type="primary"
            @click="toDetail(row)">
            查看
          </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
  </div>
</template>

<script setup>
  import { getRole } from '@/apis/userManage'
  import { spaceId } from '@/utils/config.js'

  const router = useRouter()

  const tableData = ref([])

  function indexMethod(index) {
    return index + 1
  }

  async function getRoleList() {
    try {
      const {
        data: { data: res },
      } = await getRole({ spaceId })
      tableData.value = res
    } catch (e) {
      console.log(e)
    }
  }

  onMounted(() => {
    getRoleList()
  })

  function toDetail(item) {
    router.push(`/user/role/detail?role=${item.roleName}`)
  }
</script>

<style lang="scss" scoped>
  .pageBox {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    border-radius: 6px;
    background-color: #fff;

    .title {
      margin-bottom: 20px;
      color: #303133;
      font-weight: 600;
      font-size: 20px;
      line-height: 28px;
    }

    .pagination {
      display: flex;
      flex-direction: row-reverse;
      margin-top: 20px;
    }
  }
</style>
