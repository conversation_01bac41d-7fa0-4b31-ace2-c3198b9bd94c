<template>
  <ElDialog
    v-model="model"
    width="480"
    destroy-on-close
    :before-close="onCancel"
    :close-on-click-modal="false"
    title="重置密码">
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="right"
      label-width="100px">
      <div class="tip">新密码为8-20位数字与字母组合（区分大小写）</div>
      <ElFormItem
        prop="password"
        required>
        <template #label>
          <span>设置密码</span>
        </template>
        <ElInput
          v-model="formData.password"
          show-password
          type="password"
          placeholder="请输入密码" />
      </ElFormItem>
      <ElFormItem
        required
        label="确认密码"
        prop="passwrod2">
        <ElInput
          v-model="formData.passwrod2"
          show-password
          type="password"
          placeholder="请再次输入密码" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton
        :loading="loading"
        @click="onCancel">
        取消
      </ElButton>
      <ElButton
        :loading="loading"
        type="primary"
        @click="onSubmit">
        确认
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
  import { resetPassword } from '@/apis/userManage'

  const props = defineProps({
    // 账号数据
    account: {
      type: Object,
      default: () => ({}),
    },
  })

  const model = defineModel({
    type: Boolean,
  })
  const formRef = ref(null)
  const loading = ref(false)

  const formRules = reactive({
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      {
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,20}$/,
        message: '密码为8-20位数字与字母组合（区分大小写）',
        trigger: 'blur',
      },
    ],
    passwrod2: [
      {
        validator: (_, value, cb) => {
          if (!value) {
            cb(new Error('请再次输入密码'))
          } else {
            if (value !== formData.value.password) {
              cb(new Error('输入的密码不一致'))
            } else {
              cb()
            }
          }
        },
        trigger: 'blur',
      },
    ],
  })

  const formData = ref({
    password: '',
    passwrod2: '',
  })

  function onCancel() {
    model.value = false
    formRef.value.resetFields()
  }

  async function onSubmit() {
    const result = await formRef.value.validate()
    if (result) {
      try {
        loading.value = true
        await resetPassword({
          userId: props.account.id,
          password: formData.value.password,
          confirmPassword: formData.value.passwrod2,
        })

        // 成功提示
        ElNotification({
          title: '密码已成功重置',
          message: '该用户账号密码已重置，请留意。',
          type: 'success',
        })
        loading.value = false
        model.value = false
        formRef.value.resetFields()
      } catch (e) {
        console.log(e)
      } finally {
        loading.value = false
      }
    }
  }
</script>

<style lang="scss" scoped>
  .tip {
    margin-bottom: 12px;
    color: #606266;
    font-size: 14px;
    line-height: 22px;
  }
</style>
