<template>
  <div class="filterBox">
    <div class="flex w-[345px] items-center justify-between">
      <span>姓名</span>
      <ElInput
        v-model="name"
        class="input"
        placeholder="请输入关键词" />
    </div>
    <div class="flex w-[345px] items-center justify-between">
      <span>角色</span>
      <ElSelect
        v-model="role"
        class="input"
        placeholder="不限">
        <ElOption
          v-for="item in roleListStore.roleList"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </ElSelect>
    </div>
    <div class="flex items-center justify-between">
      <ElButton
        type="primary"
        @click="onSearch">
        筛选
      </ElButton>
      <ElButton @click="onReset">重置</ElButton>
    </div>
  </div>
</template>

<script setup>
  import { getRole } from '@/apis/userManage'
  import { spaceId } from '@/utils/config.js'
  import { useRoleListStore } from '@/stores/role'

  const emits = defineEmits(['onSearch', 'onReset'])

  const roleListStore = useRoleListStore()

  async function getRoleList() {
    try {
      const {
        data: { data: res },
      } = await getRole({ spaceId })
      roleListStore.roleList = res.map((item) => ({
        label: item.roleName,
        value: item.roleCode,
      }))
    } catch (e) {
      console.log(e)
    }
  }

  onMounted(() => {
    getRoleList()
  })

  const name = defineModel('name', {
    type: String,
    default: '',
  })

  const role = defineModel('role', {
    type: String,
    default: '',
  })

  function onSearch() {
    emits('onSearch')
  }

  function onReset() {
    emits('onReset')
  }
</script>

<style lang="scss" scoped>
  .filterBox {
    display: flex;
    align-items: center;
    gap: 0 24px;

    .input {
      width: 306px;
    }
  }
</style>
