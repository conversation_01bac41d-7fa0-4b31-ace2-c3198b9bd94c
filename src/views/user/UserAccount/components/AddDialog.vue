<template>
  <ElDialog
    v-model="model"
    destroy-on-close
    width="480"
    :before-close="onCancel"
    :close-on-click-modal="false"
    title="新增账号">
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="right"
      label-width="100px">
      <ElFormItem
        prop="account"
        required>
        <template #label>
          <span>登录账号</span>
          <ElTooltip
            placement="top"
            content="登录账号为8-20位数字与小写字母组合，不可重复">
            <i
              class="iconfont icon-info-filled ml-[4px] cursor-pointer text-[12px]" />
          </ElTooltip>
        </template>
        <ElInput
          v-model="formData.account"
          placeholder="请输入用户账号" />
      </ElFormItem>
      <ElFormItem
        required
        label="姓名"
        prop="name">
        <ElInput
          v-model="formData.name"
          placeholder="请输入用户姓名信息" />
      </ElFormItem>
      <ElFormItem
        required
        label="角色"
        prop="role">
        <ElSelect
          v-model="formData.role"
          placeholder="请选择用户角色信息">
          <ElOption
            v-for="item in roleList"
            :key="item.roleCode"
            :label="item.label"
            :value="item.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem
        prop="password"
        required>
        <template #label>
          <span>设置密码</span>
          <ElTooltip
            placement="top"
            content="密码为8-20位数字与字母组合（区分大小写）">
            <i
              class="iconfont icon-info-filled ml-[4px] cursor-pointer text-[12px]" />
          </ElTooltip>
        </template>
        <ElInput
          v-model="formData.password"
          show-password
          type="password"
          placeholder="请输入密码" />
      </ElFormItem>
      <ElFormItem
        required
        label="确认密码"
        prop="passwrod2">
        <ElInput
          v-model="formData.passwrod2"
          show-password
          type="password"
          placeholder="请再次输入密码" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton
        :loading
        @click="onCancel">
        取消
      </ElButton>
      <ElButton
        :loading
        type="primary"
        @click="onSubmit">
        确认
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
  import { addAccount } from '@/apis/userManage'
  import { spaceId } from '@/utils/config.js'
  import { useRoleListStore } from '@/stores/role'

  const roleListStore = useRoleListStore()

  const { roleList } = storeToRefs(roleListStore)

  const model = defineModel({
    type: Boolean,
  })

  const formRef = ref(null)

  const emits = defineEmits(['onSuccess'])

  const formRules = reactive({
    account: [
      { required: true, message: '请输入登录账号', trigger: 'blur' },
      {
        pattern: /^(?=.*[a-z])(?=.*\d)[a-z\d]{8,20}$/,
        message: '登录账号为8-20位数字与小写字母组合',
        trigger: 'blur',
      },
    ],
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    role: [{ required: true, message: '请选择角色', trigger: 'blur' }],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      {
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,20}$/,
        message: '密码为8-20位数字与字母组合（区分大小写）',
        trigger: 'blur',
      },
    ],
    passwrod2: [
      {
        validator: (rule, value, cb) => {
          if (!value) {
            cb(new Error('请再次输入密码'))
          } else {
            if (value !== formData.value.password) {
              cb(new Error('输入的密码不一致'))
            } else {
              cb()
            }
          }
        },
        trigger: 'blur',
      },
    ],
  })

  const formData = ref({
    account: '',
    name: '',
    role: '',
    password: '',
    passwrod2: '',
  })

  function onCancel() {
    formRef.value.resetFields()
    model.value = false
  }

  const loading = ref(false)

  async function onSubmit() {
    const result = await formRef.value.validate()
    if (result) {
      try {
        loading.value = true
        await addAccount({
          spaceId,
          userName: formData.value.account,
          name: formData.value.name,
          role: formData.value.role,
          password: formData.value.password,
          confirmPassword: formData.value.passwrod2,
        })
        ElMessage.success('账号新增成功')
        emits('onSuccess')
        loading.value = false
        model.value = false
        formRef.value.resetFields()
      } catch (e) {
        console.log(e)
      } finally {
        loading.value = false
      }
    }
  }
</script>

<style lang="scss" scoped></style>
