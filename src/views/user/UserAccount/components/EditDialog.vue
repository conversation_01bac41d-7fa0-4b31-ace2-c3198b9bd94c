<template>
  <ElDialog
    v-model="model"
    width="480"
    destroy-on-close
    :before-close="onCancel"
    :close-on-click-modal="false"
    title="编辑账号">
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="right"
      label-width="100px">
      <ElFormItem
        prop="account"
        label="登录账号">
        <ElInput
          :value="account.username"
          disabled />
      </ElFormItem>
      <ElFormItem
        required
        label="姓名"
        prop="fullName">
        <ElInput
          v-model="formData.fullName"
          placeholder="请输入用户姓名信息" />
      </ElFormItem>
      <ElFormItem
        required
        label="角色"
        prop="roleCode">
        <ElSelect
          v-model="formData.roleCode"
          placeholder="请选择用户角色信息">
          <ElOption
            v-for="item in roleList"
            :key="item.roleCode"
            :label="item.label"
            :value="item.value" />
        </ElSelect>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton
        :loading="loading"
        @click="onCancel">
        取消
      </ElButton>
      <ElButton
        :loading="loading"
        type="primary"
        @click="onSubmit">
        确认
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
  import { editAccount } from '@/apis/userManage'
  import { spaceId } from '@/utils/config.js'
  import { useRoleListStore } from '@/stores/role'

  const props = defineProps({
    // 账号数据
    account: {
      type: Object,
      default: () => ({}),
    },
  })

  const roleListStore = useRoleListStore()

  const { roleList } = storeToRefs(roleListStore)

  const model = defineModel({
    type: Boolean,
  })

  const emits = defineEmits(['onSuccess'])

  const formRef = ref(null)

  const loading = ref(false)

  const formRules = reactive({
    fullName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    roleCode: [{ required: true, message: '请选择角色', trigger: 'blur' }],
  })

  const formData = ref({
    fullName: '',
    roleCode: '',
  })

  watch(
    () => props.account,
    (nv) => {
      formData.value = { ...nv }
      formData.value.roleCode = nv.roleList?.[0]?.roleCode
    },
    {
      immediate: true,
      deep: true,
    },
  )

  function onCancel() {
    formRef.value.resetFields()
    model.value = false
  }

  async function onSubmit() {
    const result = await formRef.value.validate()
    if (result) {
      loading.value = true
      try {
        await editAccount({
          spaceId,
          userId: props.account.id,
          name: formData.value.fullName,
          role: formData.value.roleCode,
        })
        ElMessage.success('修改成功')
        model.value = false
        formRef.value.resetFields()
        emits('onSuccess')
      } catch (e) {
        console.log(e)
      } finally {
        loading.value = false
      }
    }
  }
</script>

<style lang="scss" scoped></style>
