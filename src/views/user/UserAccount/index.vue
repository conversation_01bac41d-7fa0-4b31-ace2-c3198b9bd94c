<template>
  <div class="pageContainer">
    <div class="head">
      <div class="title">账号管理</div>
      <ElButton
        v-if="useRoleControl('ADMIN')"
        class="align-center flex"
        link
        type="primary"
        @click="dialogIsShow.add = true">
        <i class="iconfont icon-circle-plus text-[12px]" />
        <span class="ml-[4px]">新增账号</span>
      </ElButton>
    </div>
    <div class="line" />
    <FilterForm
      v-model:name="filterData.name"
      v-model:role="filterData.role"
      @on-reset="onReset"
      @on-search="onSearch" />
    <ElTable
      ref="tableRef"
      v-loading="loading"
      border
      :data="tableData"
      class="mt-[20px] flex-1"
      @selection-change="handleSelectionChange">
      <ElTableColumn
        v-if="isTableSelect"
        type="selection"
        width="40" />
      <ElTableColumn
        type="index"
        label="序号"
        :index="indexMethod"
        width="60" />
      <ElTableColumn
        prop="username"
        label="登录账号" />
      <ElTableColumn
        prop="fullName"
        label="姓名" />
      <ElTableColumn
        prop="role"
        label="角色" />
      <ElTableColumn
        v-if="useRoleControl('ADMIN')"
        label="账号状态">
        <template #default="{ row, $index }">
          <ElSwitch
            :loading="statusLoading.includes(row.id)"
            :model-value="row.status"
            inline-prompt
            active-value="enabled"
            inactive-value="disabled"
            active-text="启用"
            inactive-text="禁用"
            :style="`
              --el-switch-on-color: #13ce66;
              --el-switch-off-color: #ff4949;
            `"
            @change="onAccountStatusChange(row, $index)" />
        </template>
      </ElTableColumn>
      <ElTableColumn label="操作">
        <template #default="{ row }">
          <ElButton
            link
            type="success"
            :disabled="!useRoleControl('ADMIN')"
            @click="onResetPassword(row)">
            重置密码
          </ElButton>
          <ElButton
            link
            :disabled="!useRoleControl('ADMIN')"
            type="primary"
            @click="onEdit({ ...row })">
            编辑
          </ElButton>
          <ElButton
            v-show="!isTableSelect"
            link
            :disabled="!useRoleControl('ADMIN')"
            type="danger"
            @click="onDelete(row)">
            删除
          </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
    <div class="footer">
      <div>
        <div v-if="useRoleControl('ADMIN')">
          <ElButton
            v-if="!isTableSelect"
            link
            type="danger"
            @click="isTableSelect = true">
            <div class="flex items-center text-[12px]">
              <i class="iconfont icon-delete text-[12px]" />
              <span class="ml-[5px]">批量删除</span>
            </div>
          </ElButton>
          <template v-else>
            <ElButton
              link
              type="danger"
              @click="onDeleteMore">
              删除
            </ElButton>
            <ElButton
              link
              @click="onCancelSelect">
              取消
            </ElButton>
          </template>
        </div>
      </div>
      <ElPagination
        v-model:page-size="paginate.pageSize"
        v-model:current-page="paginate.pageNum"
        layout="total, sizes,prev,pager,next, jumper"
        :total="paginate.total" />
    </div>
  </div>
  <!-- 新增 -->
  <AddDialog
    v-model="dialogIsShow.add"
    @on-success="onReset" />
  <!-- 重置密码 -->
  <ResetPassword
    v-model="dialogIsShow.reset"
    :account="handleAccount" />
  <!-- 编辑 -->
  <EditDialog
    v-model="dialogIsShow.edit"
    :account="handleAccount"
    @on-success="onReset" />
</template>

<script setup>
  import {
    batchDeleteAccount,
    checkoutStatus,
    getUserList,
    removeAccount,
  } from '@/apis/userManage'
  import { useRoleControl } from '@/utils'
  import { spaceId } from '@/utils/config.js'
  import AddDialog from './components/AddDialog.vue'
  import EditDialog from './components/EditDialog.vue'
  import FilterForm from './components/FilterForm.vue'
  import ResetPassword from './components/ResetPassword.vue'

  const tableRef = ref(null)

  const loading = ref(false)

  const filterData = ref({
    name: '',
    role: '',
  })

  const tableData = ref([])

  // 分页
  const paginate = ref({
    pageSize: 20,
    total: 0,
    pageNum: 1,
  })

  function getRoleStr(roleList) {
    let roleStr = roleList.map((role) => role.roleName).join(',')
    return roleStr
  }

  async function getAccountList() {
    try {
      loading.value = true
      const param = {
        spaceId,
        pageNum: paginate.value.pageNum,
        pageSize: paginate.value.pageSize,
        ...filterData.value,
      }
      const {
        data: { data: res },
      } = await getUserList(param)
      paginate.value.total = res.total
      tableData.value = res.list.map((item) => {
        return {
          ...item,
          role: getRoleStr(item.roleList),
        }
      })
    } catch (e) {
      console.log(e)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    getAccountList()
  })

  function onReset() {
    filterData.value = {
      name: '',
      role: '',
    }
    paginate.value = {
      pageSize: 20,
      total: 0,
      pageNum: 1,
    }
    getAccountList()
  }

  function onSearch() {
    getAccountList()
  }

  const isTableSelect = ref(false)

  function indexMethod(index) {
    return (paginate.value.pageNum - 1) * paginate.value.pageSize + index + 1
  }

  // 监听分页
  watch(
    () => paginate.value.pageSize,
    () => {
      getAccountList()
    },
  )
  watch(
    () => paginate.value.pageNum,
    () => {
      getAccountList()
    },
  )

  const selectItems = ref([])

  function handleSelectionChange(val) {
    selectItems.value = val
  }

  // 取消批量删除
  function onCancelSelect() {
    tableRef.value.clearSelection()
    selectItems.value = []
    isTableSelect.value = false
  }

  // 操作的账号
  const handleAccount = ref({})

  const dialogIsShow = reactive({
    add: false,
    reset: false,
    edit: false,
  })

  // 重置密码
  function onResetPassword(item) {
    handleAccount.value = item
    dialogIsShow.reset = true
  }

  // 编辑账号
  function onEdit(item) {
    handleAccount.value = item
    dialogIsShow.edit = true
  }

  // 控制账号开关loading
  const statusLoading = ref([])
  // 切换账号状态
  async function onAccountStatusChange(item, index) {
    try {
      !statusLoading.value.includes(item.id) &&
        statusLoading.value.push(item.id)
      const statusParam = item.status === 'enabled' ? 'disabled' : 'enabled'
      await checkoutStatus({
        userId: item.id,
        status: statusParam,
      })
      tableData.value.find((_, i) => i == index).status = statusParam
      ElMessage({
        message: '账号状态切换成功',
        type: 'success',
        grouping: 'status',
      })
    } catch (e) {
      console.log(e)
    } finally {
      let i = statusLoading.value.indexOf(item.id)
      i !== -1 && statusLoading.value.splice(i, 1)
    }
  }

  // 删除弹窗
  function onDelete(item) {
    ElMessageBox.confirm(
      '提示：删除成功则该账号关联的所有数据也将同时删除。',
      '确认删除当前账号？',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
      .then(async () => {
        try {
          await removeAccount({
            userId: item.id,
          })
        } catch (e) {
          console.log(e)
        }
        // 删除成功提示
        ElMessage.success('删除成功')
        onReset()
      })
      .catch(() => {})
  }

  // 批量删除弹窗
  async function onDeleteMore() {
    if (selectItems.value.length === 0) {
      ElMessage.info('请选择账号')
      return
    }
    ElMessageBox.confirm(
      '提示：删除成功则当前所有选中账号关联的所有数据也将同时删除。',
      '确认删除当前所有选中账号？',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
      .then(async () => {
        try {
          await batchDeleteAccount(selectItems.value.map((item) => item.id))
          // 删除成功提示
          ElMessage.success('删除成功')
          onReset()
        } catch (e) {
          console.log(e)
        }
      })
      .catch(() => {})
  }
</script>

<style lang="scss" scoped>
  .pageContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    border-radius: 4px;
    background-color: #fff;

    .head {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        color: #303133;
        font-weight: 600;
        font-size: 20px;
        line-height: 28px;
      }
    }

    .line {
      margin: 12px 0;
      border-bottom: 1px solid #dcdfe6;
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 26px;
    }
  }
</style>
