export const resumeList = [
  {
    id: '12',
    bid: '1808789819092193280',
    name: '陈跃',
    avatarUrl: 'https://example.com/avatar/zhangsan.jpg',
    qualificationn: 2,
    academy: '湖北工程学院新技术学院',
    major: '财务管理',
    working: '深圳市腾讯计算机系统有限公司',
    position: '运营专员',
    schoolNature: '双非',
    score: 77,
    education: '学士',
    skill: ['运营管理'],
  },
  {
    id: '13',
    bid: '1808793625842495488',
    name: '陈晓丽',
    avatarUrl: 'https://example.com/avatar/lisi.jpg',
    qualificationn: 2,
    academy: '北京大学',
    major: '经济学',
    working: '北京金融科技有限公司',
    position: '项目经理',
    schoolNature: '985',
    score: 92,
    education: '博士',
    skill: ['Java'],
  },
  {
    id: '14',
    bid: '1808793818298134528',
    name: '杨慧婷',
    avatarUrl: 'https://example.com/avatar/lisi.jpg',
    qualificationn: 1,
    academy: '北京大学',
    major: '现代农业经济管理',
    working: '北京金融科技有限公司',
    position: '项目经理',
    score: 99,
    education: '博士',
    skill: ['Go', 'Php'],
  },
  {
    id: '15',
    bid: '1808794016395112448',
    name: '郑晨晨',
    avatarUrl: 'https://example.com/avatar/lisi.jpg',
    qualificationn: 0,
    academy: '北京大学',
    major: '园林技术',
    working: '北京金融科技有限公司',
    position: '项目经理',
    schoolNature: '985',
    score: 83,
    education: '本科',
    skill: ['JavaScript', 'Vue'],
  },
  {
    id: '16',
    bid: '1808794137396588544',
    name: '黄慧敏',
    avatarUrl: 'https://example.com/avatar/wangwu.jpg',
    qualificationn: 1,
    academy: '上海交通大学',
    major: '机械工程',
    working: '上海智能制造有限公司',
    position: '研发工程师',
    schoolNature: '211',
    score: 90,
    education: '硕士',
    skill: ['React'],
  },
  {
    id: '17',
    bid: '1812660609311145984',
    name: '赵高',
    avatarUrl: 'https://example.com/avatar/wangwu.jpg',
    qualificationn: 3,
    academy: '复旦大学',
    major: '计算机科学与技术',
    working: '上海智能制造有限公司',
    position: '研发工程师',
    schoolNature: '985',
    score: 89,
    education: '本科',
    skill: ['Python', 'Ruby'],
  },
  {
    id: '18',
    bid: '1812662738763476992',
    name: '吴晓晨',
    avatarUrl: 'https://example.com/avatar/wangwu.jpg',
    qualificationn: 3,
    academy: '清华大学',
    major: '计算机科学与技术',
    working: '上海智能制造有限公司',
    position: '研发工程师',
    schoolNature: '985',
    score: 90,
    education: '本科',
    skill: ['C#', 'Swift'],
  },
  {
    id: '19',
    bid: '1812662948210241536',
    name: '陈晓锋',
    avatarUrl: 'https://example.com/avatar/wangwu.jpg',
    qualificationn: 3,
    academy: '北京大学',
    major: '计算机科学与技术',
    working: '上海智能制造有限公司',
    position: '研发工程师',
    schoolNature: '211',
    score: 91,
    education: '博士',
    skill: ['C#', 'Java'],
  },
  {
    id: '20',
    bid: '1812663105379201024',
    name: '刘家乐',
    avatarUrl: 'https://example.com/avatar/wangwu.jpg',
    qualificationn: 3,
    academy: '北京大学',
    major: '网络空间安全',
    working: '上海智能制造有限公司',
    position: '研发工程师',
    schoolNature: '985',
    score: 86,
    education: '硕士',
    skill: ['Swift', 'Go'],
  },
  {
    id: '21',
    bid: '1812663396241600512',
    name: '张鑫',
    avatarUrl: 'https://example.com/avatar/wangwu.jpg',
    qualificationn: 3,
    academy: '北京大学',
    major: '农业资源与环境',
    working: '上海智能制造有限公司',
    position: '研发工程师',
    schoolNature: '985',
    score: 99,
    education: '博士',
    skill: ['python', 'Php'],
  },
  {
    id: '22',
    bid: '1812663583324336128',
    name: '杨中原',
    avatarUrl: 'https://example.com/avatar/wangwu.jpg',
    qualificationn: 3,
    academy: '北京大学',
    major: '计算机科学与技术',
    working: '上海智能制造有限公司',
    position: '研发工程师',
    schoolNature: '985',
    score: 88,
    education: '本科',
    skill: ['Swift', 'C++'],
  },
  {
    id: '24',
    bid: '1812739644439404544',
    name: '郭烈',
    avatarUrl: 'http://example.com/avatar.jpg',
    qualificationn: 2,
    academy: '复旦大学',
    major: '经济学',
    working: '上海金融科技有限公司',
    position: '金融分析师',
    schoolNature: '985',
    score: 90,
    education: '博士',
    skill: ['JavaScript', 'Ruby'],
  },
  {
    id: '25',
    bid: '1812740771151093760',
    name: '李里',
    avatarUrl: 'https://example.com/photos/avatar_wangwu.jpg',
    qualificationn: 1,
    academy: '北京邮电大学',
    major: '计算机科学与技术',
    working: '北京创新科技有限公司',
    position: '前端开发工程师',
    schoolNature: '211',
    score: 93,
    education: '硕士',
    skill: ['Go', 'Python'],
  },
]
