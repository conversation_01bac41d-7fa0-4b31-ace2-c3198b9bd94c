<template>
  <div class="container">
    <div class="chartContainer">
      <div class="chartBox">
        <div class="title chartTitle">
          <div class="deco" />
          <div class="titleText">人才综合评价</div>
        </div>
        <div class="score">
          <div class="scoreText">人才综合评分</div>
          <div class="scoreNum">{{ score }}</div>
        </div>
        <RadarChart />
      </div>
      <div class="chartBox">
        <div class="title chartTitle">
          <div class="deco" />
          <div class="titleText">行业背景</div>
        </div>
        <RingChart />
      </div>
      <div class="chartBox">
        <div class="title chartTitle">
          <div class="deco" />
          <div class="titleText">岗位经验</div>
        </div>
        <RoseChart />
      </div>
    </div>
    <div
      v-if="data.tags"
      class="tagContainer">
      <div class="lightTag w-[50%]">
        <div class="title">
          <div class="deco" />
          <div class="titleText">人才标签</div>
        </div>
        <div class="subTitle">{{ data.tags[0].name }}</div>
        <div
          v-for="(item, index) in data.tags[0].tags"
          :key="index">
          <div
            v-if="Array.isArray(item.tags) && item.tags.length > 0"
            class="tagTitle">
            <div class="tagTitle">
              {{ item.name }}
            </div>
            <div class="tag">
              <div
                v-for="tag in item.tags"
                :key="tag"
                :style="{ color: _color(item.name) }"
                class="tagItem">
                {{ tag }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="otherTag">
        <div
          v-for="(item, index) in otherTags"
          :key="index">
          <div v-if="item.tags">
            <div class="tag_title">{{ item.name }}</div>
            <div
              class="tag flex-col"
              style="align-items: flex-start">
              <template
                v-for="tag in item.tags"
                :key="tag.name">
                <div class="tagTitle">{{ tag.name }}</div>
                <div class="tag">
                  <div
                    v-for="t in tag.tags"
                    :key="t"
                    class="tagItem"
                    :style="{ color: _color(t) }">
                    {{ t }}
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div v-else-if="item.directlyTags">
            <div class="tag_title">{{ item.name }}</div>
            <div class="tag">
              <div
                v-for="tag in item.directlyTags"
                :key="tag"
                class="tagItem"
                :style="{ color: _color(item) }">
                <template v-if="typeof tag === 'string'">
                  {{ tag }}
                </template>
                <template v-else>
                  {{ tag }}
                </template>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="subTitle">风险点标签</div>
        <div class="tag">
          <div class="tagItem risk">较长空白期</div>
        </div>
        <div class="subTitle">问题点标签</div>
        <div class="tag">
          <div class="tagItem question">培训经历与工作经历不符</div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
  import eventBus from '@/plugins/event-bus.js'
  import RadarChart from '@/views/resume/talents/components/radar-chart.vue'
  import RingChart from '@/views/resume/talents/components/ring-chart.vue'
  import RoseChart from '@/views/resume/talents/components/rose-chart.vue'
  import { colors } from '@/views/tag/config'

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({ tags: [] }),
    },
  })
  const score = ref(0)
  eventBus.on('score', (val) => {
    score.value = val
  })
  const { data } = toRefs(props)
  const _color = (name) => {
    const data = colors.find((item) => item.name === name)
    return data ? data.color : '#409EFF'
  }
  const otherTags = computed(() => {
    return data.value.tags.filter((item) => item.name !== '亮点标签')
  })
</script>

<style lang="scss" scoped>
  .container {
    overflow-y: auto;

    .chartContainer {
      display: flex;
      gap: 16px;

      .chartBox {
        position: relative;
        width: 539px;
        height: 393px;
        border-radius: 4px;
        background: #fafafa;

        .chartTitle {
          margin: 0;
          padding: 24px 0 20px;
        }

        .score {
          display: flex;
          position: absolute;
          top: 68px;
          right: 0;
          align-items: center;
          width: 194px;
          height: 36px;
          padding-left: 40px;
          background: url('@/assets/images/talents-score-bg.png') no-repeat;

          .scoreText {
            margin-right: 10px;
            color: #a8abb2;
            font-size: 12px;
          }

          .scoreNum {
            color: #05f;
            font-weight: 600;
            font-size: 28px;
          }
        }
      }
    }

    .tagContainer {
      display: flex;

      .title {
        display: flex;
        align-items: center;
        margin: 20px 0;
        background: #fff;

        .deco {
          width: 4px;
          height: 16px;
          margin-right: 12px;
          border-radius: 2px;
          background: #05f;
        }
      }

      .subTitle {
        margin-bottom: 16px;
        color: #303133;
        font-weight: bold;
        font-size: 16px;
      }

      .otherTag {
        padding-top: 64px;
      }

      .tagTitle {
        margin-bottom: 8px;
        color: #303133;
        font-size: 16px;
      }

      .tag_title {
        margin-bottom: 8px;
        color: #303133;
        font-weight: bold;
        font-size: 16px;
      }

      .tag {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 12px;
        gap: 10px;

        .tagItem {
          height: 20px;
          padding: 0 8px;
          border-radius: 4px;
          background: #f5f7fa;
          font-size: 12px;
          line-height: 20px;
        }

        .education {
          color: #409eff;
        }

        .job {
          color: #67c23a;
        }

        .skill {
          color: #909399;
        }

        .others {
          color: #13c2c2;
        }

        .risk {
          color: #e6a23c;
        }

        .question {
          color: #f56c6c;
        }

        .political {
          color: #a0d911;
        }

        .language {
          color: #eb2f96;
        }

        .academic {
          color: #2f54eb;
        }

        .race {
          color: #7a2feb;
        }
      }
    }
  }
</style>
