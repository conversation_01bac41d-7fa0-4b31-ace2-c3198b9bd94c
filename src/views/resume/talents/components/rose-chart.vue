<template>
  <VChart
    v-if="isShow"
    ref="rose"
    :option="option"
    class="rose" />
</template>

<script setup>
  import * as apis from '@/apis/resume'

  const route = useRoute()
  const rose = ref()

  const option = ref({
    color: ['#0055FF', '#14C9C9', '#F7BA1E', '#722ED1'],
    series: [
      {
        type: 'pie',
        radius: ['0', '75%'],
        center: ['50%', '50%'],
        roseType: 'radius',
        itemStyle: {
          borderWidth: 4,
          borderColor: '#fafafa',
        },
        label: {
          formatter(param) {
            return param.data.name + '\n\n' + param.data.time
          },
          color: '#909399',
        },
        data: [
          { value: 40, name: '研发工程师', time: '2013.01 - 2017.02' },
          { value: 34, name: '售前', time: '2013.01 - 2017.02' },
          { value: 26, name: '产品运营', time: '2013.01 - 2017.02' },
          { value: 20, name: '设计师', time: '2013.01 - 2017.02' },
        ],
      },
    ],
  })
  const isShow = ref(true)
  const getResumeInfoExp = async () => {
    const res = await apis.getResumeInfoExp({ talentId: route.query.id })
    if (res.data.data.length === 0) {
      isShow.value = false
      return
    }
    option.value.series[0].data = res.data.data.map((item) => {
      return {
        value: item.length,
        name: item.jobTitle,
        time: `${item.startDate} - ${item.endDate}`,
      }
    })
  }
  onMounted(() => {
    getResumeInfoExp()
  })
</script>

<style lang="scss" scoped>
  .rose {
    width: 100%;
    height: calc(100% - 68px);
  }
</style>
