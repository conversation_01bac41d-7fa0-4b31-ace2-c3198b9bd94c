<template>
  <div
    ref="ring"
    class="ring" />
</template>

<script setup>
  import { resumeGetIndusty } from '@/apis/resume'
  import * as echarts from 'echarts'

  const route = useRoute()
  const ring = ref()

  watchEffect(() => {
    resumeGetIndusty({ talentId: route.query.id }).then((res) => {
      const data = res.data.data
      let series = []
      let yAxis = []

      if (Array.isArray(data) && data.length > 0) {
        let value = data.map((item) => parseFloat(item.value) || 0)
        let sum = value.reduce((a, b) => {
          return a + b
        })

        for (let i = 0; i < data.length; i++) {
          series.push({
            type: 'pie',
            clockWise: true, //顺时加载
            radius: [75 - i * 10 + '%', 70 - i * 10 + '%'],
            center: ['50%', '50%'],
            label: {
              show: false,
            },
            itemStyle: {
              borderRadius: 30,
            },
            tooltip: {
              trigger: 'item',
              show: true,
              formatter: ({ name, percent }) => {
                return `${name} : ${percent}%`
              },
            },
            data: [
              {
                value: data[i].value,
                name: data[i].name,
              },
              {
                value: sum - data[i].value,
                name: '',
                itemStyle: {
                  color: 'transparent',
                },
              },
            ],
          })
          series.push({
            type: 'pie',
            z: 1,
            clockWise: true, //顺时加载
            radius: [75 - i * 10 + '%', 70 - i * 10 + '%'],
            center: ['50%', '50%'],
            label: {
              show: true,
            },
            itemStyle: {
              borderRadius: 30,
            },
            tooltip: {
              show: false,
            },
            data: [
              {
                value: 7.5,
                itemStyle: {
                  color: 'transparent',
                },
              },
              {
                value: 2.5,
                itemStyle: {
                  color: 'rgba(0,0,0,0)',
                },
              },
            ],
          })
          yAxis.push(((data[i].value / sum) * 100).toFixed(2) + '%')
        }
      }

      const option = {
        color: ['#0055FF', '#14C9C9', '#F7BA1E'],
        grid: {
          top: '12%',
          left: '50%',
          height: '14%',
        },
        tooltip: {
          trigger: 'item',
          show: true,
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              formatter(value, index) {
                return `${data[index]?.name || ''} ${value}`
              },
              interval: 0,
              color: '#909399',
              fontSize: 12,
              show: true,
            },
            data: yAxis,
          },
        ],
        xAxis: [
          {
            show: false,
          },
        ],
        series,
      }
      const ringChart = echarts.init(ring.value)
      ringChart.setOption(option)
    })
  })
</script>

<style lang="scss" scoped>
  .ring {
    width: 100%;
    height: calc(100% - 68px);
  }
</style>
