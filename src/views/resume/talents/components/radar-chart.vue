<template>
  <VChart
    ref="radar"
    :option="option"
    autoresize
    class="h-full w-full" />
</template>

<script setup>
  import * as apis from '@/apis/resume'
  import eventBus from '@/plugins/event-bus.js'

  const route = useRoute()
  const radar = ref()
  const resumeGetTalentScore = async () => {
    try {
      const res = await apis.resumeGetTalentScore({ talentId: route.query.id })
      const { list, overallScore } = res.data.data
      eventBus.emit('score', overallScore)
      const count = {
        教育背景: 30,
        工作能力: 50,
        语言能力: 5,
        荣誉指数: 5,
        专业指数: 10,
      }
      option.radar.indicator = list.map((item) => ({
        name: item.name,
        max: count[item.name] || 0,
      }))
      option.series[0].data[0].value = list.map((item) => item.score)
    } catch (error) {
      console.log(error)
    }
  }
  const option = reactive({
    tooltip: {
      borderColor: 'transparent',
    },
    radar: {
      indicator: [
        {
          name: '技能评分',
          max: 100,
        },
        { name: '工作能力', max: 100 },
        { name: '心理测评', max: 100 },
        { name: '教育背景', max: 100 },
        { name: '稳定性评分', max: 100 },
      ],
    },
    series: [
      {
        name: '人才综合评价',
        type: 'radar',
        symbolSize: 6,
        itemStyle: {
          borderWidth: 2,
          borderColor: '#fff',
        },
        areaStyle: {
          color: 'rgba(0,85,255,0.1)',
        },
        data: [
          {
            value: [60, 50, 30, 70, 80],
          },
        ],
      },
    ],
  })

  onMounted(() => {
    resumeGetTalentScore()
  })
</script>

<style lang="scss" scoped>
  .radar {
    width: 100%;
    height: calc(100% - 68px);
  }
</style>
