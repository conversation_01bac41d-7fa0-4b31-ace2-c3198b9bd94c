<template>
  <div class="resume_original">
    <ElTabs type="border-card">
      <ElTabPane label="简历信息">
        <ResumeInfo />
      </ElTabPane>
      <ElTabPane label="简历标签">
        <ResumeTag />
      </ElTabPane>
    </ElTabs>
  </div>
</template>

<script setup>
  import ResumeInfo from './resume-info.vue'
  import ResumeTag from './resume-tag.vue'
</script>

<style lang="scss" scoped>
  .resume_original {
    flex: 1;
    height: 100%;

    &:deep(.el-tabs) {
      .is-active {
        color: #000;
      }

      .el-tabs__item:hover {
        color: #000 !important;
      }
    }
  }
</style>
