<template>
  <div class="resume_form">
    <h2 class="title">简历原件</h2>
    <main class="main">
      <iframe
        id="iframe"
        class="iframe"
        :src="url"
        frameborder="0"
        width="100%"
        height="100%"
        scrolling="auto" />
    </main>
  </div>
</template>

<script setup>
  import { kkFileViewUrl } from '@/utils/config'
  import { Base64 } from 'js-base64'

  const route = useRoute()
  const url = computed(() => {
    return `${kkFileViewUrl}?url=${encodeURIComponent(
      Base64.encode(route.query.resumeUrl),
    )}`
  })
</script>

<style lang="scss" scoped>
  .resume_form {
    display: flex;
    flex-direction: column;
    width: 590px;
    height: 100%;

    .title {
      color: #303133;
      font-weight: bold;
      font-size: 18px;
      line-height: 38px;
    }

    .main {
      flex: 1;
      border: 1px solid #f4f4f5;
      border-radius: 4px;
    }
  }
</style>
