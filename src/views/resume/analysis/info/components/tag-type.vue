<template>
  <div class="tag_type">
    <h2
      v-if="subList.length > 0 || tagList.length > 0"
      class="title">
      {{ title }}
    </h2>
    <template v-if="subList.length === 0">
      <div class="flex w-[500px] flex-wrap gap-[6px]">
        <ElTag
          v-for="item in tagList"
          :key="item"
          :type="tagType">
          {{ item }}
        </ElTag>
      </div>
    </template>
    <template v-else>
      <div
        v-for="(item, index) in subList"
        :key="index"
        class="sub_list">
        <div
          v-if="item.tags && item.tags.length > 0"
          class="mt-[10px]">
          <p class="pb-[8px] text-[14px] text-[#606266]">
            {{ item.name }}
          </p>
          <div class="flex w-[500px] flex-wrap gap-[6px]">
            <ElTag
              v-for="tag in item.tags"
              :key="tag"
              :type="tagType">
              {{ tag }}
            </ElTag>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    subList: {
      type: Array,
      default: () => [],
    },
    tagType: {
      type: String,
      default: 'primary',
    },
    tagList: {
      type: Array,
      default: () => [],
    },
  })
  const { title, subList, tagList } = toRefs(props)
  console.log(title.value)
  console.log(subList.value)
  console.log(tagList.value)
</script>

<style lang="scss" scoped>
  .tag_type {
    max-width: 500px;
    padding-bottom: 16px;

    .title {
      color: #303133;
      font-weight: bold;
      font-size: 18px;
    }
  }
</style>
