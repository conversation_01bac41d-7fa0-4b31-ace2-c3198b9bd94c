<template>
  <ElButton
    v-if="isPreview"
    class="check"
    @click="isPreview = false">
    <template #icon>
      <i class="iconfont icon-document-checked" />
    </template>
    校对
  </ElButton>
  <div
    v-else
    class="check flex">
    <ElButton
      type="primary"
      :loading="loading"
      @click="onSubmit">
      确认
    </ElButton>
    <ElButton @click="isPreview = true">取消</ElButton>
  </div>
  <main class="main">
    <div
      id="main_form"
      class="main_form">
      <ElForm
        label-width="102"
        label-position="left">
        <!-- 基本信息 -->
        <template v-if="true">
          <label id="label_1">基本信息</label>
          <!-- Preview -->
          <div
            v-show="isPreview"
            class="is_preview">
            <ElRow :gutter="32">
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.name)"
                :span="12">
                <LabelValue label="姓名">
                  {{ formData.resumeBase?.name }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.gender)"
                :span="12">
                <LabelValue label="性别">
                  {{ formData.resumeBase?.gender == '0' ? '男' : '女' }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.nationalIdentityNumber)"
                :span="12">
                <LabelValue label="身份证号">
                  {{ formData.resumeBase?.nationalIdentityNumber }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.dateOfBirth)"
                :span="12">
                <LabelValue label="出生年月">
                  {{ formData.resumeBase?.dateOfBirth }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.age)"
                :span="12">
                <LabelValue label="年龄">
                  {{ formData.resumeBase?.age }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.birthplaceArr)"
                :span="12">
                <LabelValue label="籍贯">
                  {{ formData.resumeBase?.birthplaceArr.join('-') }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.currentLocation)"
                :span="12">
                <LabelValue label="现居住地">
                  {{ formData.resumeBase?.currentLocation }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.registeredResidenceArr)"
                :span="12">
                <LabelValue label="户口所在地">
                  {{ formData.resumeBase?.registeredResidenceArr.join('-') }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.height)"
                :span="12">
                <LabelValue label="身高">
                  {{
                    formData.resumeBase?.height &&
                    `${formData.resumeBase?.height}cm`
                  }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.weight)"
                :span="12">
                <LabelValue label="体重">
                  {{
                    formData.resumeBase?.weight &&
                    `${formData.resumeBase?.weight}kg`
                  }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.ethnic)"
                :span="12">
                <LabelValue label="民族">
                  {{ formData.resumeBase?.ethnic }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.maritalStatus)"
                :span="12">
                <LabelValue label="婚姻状况">
                  {{ formData.resumeBase?.maritalStatus }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.politicalStatus)"
                :span="12">
                <LabelValue label="政治面貌">
                  {{ formData.resumeBase?.politicalStatus }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.degree)"
                :span="12">
                <LabelValue label="最高学历">
                  {{
                    {
                      '0': '大专以下',
                      '1': '大专',
                      '2': '本科',
                      '3': '硕士',
                      '4': '博士',
                    }[formData.resumeBase?.degree] || '本科'
                  }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.schoolName)"
                :span="12">
                <LabelValue label="学校名称">
                  {{ formData.resumeBase?.schoolName }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.major)"
                :span="12">
                <LabelValue label="专业名称">
                  {{ formData.resumeBase?.major }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.currentCompany)"
                :span="12">
                <LabelValue label="当前公司">
                  {{ formData.resumeBase?.currentCompany }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.currentPosition)"
                :span="12">
                <LabelValue label="当前职位">
                  {{ formData.resumeBase?.currentPosition }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.professionalTitle)"
                :span="12">
                <LabelValue label="职称级别">
                  {{ formData.resumeBase?.professionalTitle }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.desiredPosition)"
                :span="12">
                <LabelValue label="应聘职位">
                  {{ formData.resumeBase?.desiredPosition }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.expectedSalary)"
                :span="12">
                <LabelValue label="期望薪资">
                  {{
                    formData.resumeBase?.expectedSalary &&
                    `${
                      formData.resumeBase?.expectedSalary.endsWith('元')
                        ? formData.resumeBase?.expectedSalary
                        : formData.resumeBase?.expectedSalary + '元'
                    }`
                  }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.expectLocation)"
                :span="12">
                <LabelValue label="期望工作地点">
                  {{ formData.resumeBase?.expectLocation }}
                </LabelValue>
              </ElCol>
            </ElRow>
          </div>
          <!-- Form -->
          <div
            v-show="!isPreview"
            class="is_edit">
            <ElRow :gutter="32">
              <ElCol :span="12">
                <ElFormItem label="姓名">
                  <ElInput v-model="formData.resumeBase.name" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="性别">
                  <ElSelect
                    v-model="formData.resumeBase.gender"
                    :disabled="true">
                    <ElOption
                      label="男"
                      :value="0" />
                    <ElOption
                      label="女"
                      :value="1" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="身份证号">
                  <ElInput
                    v-model="formData.resumeBase.nationalIdentityNumber" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="出生年月">
                  <ElInput
                    v-model="formData.resumeBase.dateOfBirth"
                    :disabled="true" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="年龄">
                  <ElInput
                    v-model="formData.resumeBase.age"
                    :disabled="true">
                    <template #append>岁</template>
                  </ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="籍贯">
                  <ElCascader
                    v-model="formData.resumeBase.birthplaceArr"
                    :options="cityData"
                    :props="{
                      label: 'name',
                      value: 'name',
                      checkStrictly: true,
                    }"
                    separator="-"
                    class="w-full" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem>
                  <template #label>
                    <span class="mr-[4px]">现居住地</span>
                    <i class="iconfont icon-info-filled text-[12px]!" />
                  </template>
                  <ElInput v-model="formData.resumeBase.currentLocation" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="户口所在地">
                  <ElCascader
                    v-model="formData.resumeBase.registeredResidenceArr"
                    :options="cityData"
                    :props="{
                      label: 'name',
                      value: 'name',
                      checkStrictly: true,
                    }"
                    class="w-full" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="身高">
                  <ElInput v-model="formData.resumeBase.height">
                    <template #append>cm</template>
                  </ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="体重">
                  <ElInput v-model="formData.resumeBase.weight">
                    <template #append>kg</template>
                  </ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="民族">
                  <ElSelect v-model="formData.resumeBase.ethnic">
                    <ElOption
                      v-for="item in nationData"
                      :key="item"
                      :label="item"
                      :value="item" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="婚姻状况">
                  <ElInput v-model="formData.resumeBase.maritalStatus" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="政治面貌">
                  <ElInput v-model="formData.resumeBase.politicalStatus" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="最高学历">
                  <ElSelect
                    v-model="formData.resumeBase.degree"
                    :disabled="true">
                    <ElOption
                      label="大专以下"
                      :value="0" />
                    <ElOption
                      label="大专"
                      :value="1" />
                    <ElOption
                      label="本科"
                      :value="2" />
                    <ElOption
                      label="硕士"
                      :value="3" />
                    <ElOption
                      label="博士"
                      :value="4" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="学校名称">
                  <ElInput
                    v-model="formData.resumeBase.schoolName"
                    :disabled="true" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="专业名称">
                  <ElInput
                    v-model="formData.resumeBase.major"
                    :disabled="true" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="当前公司">
                  <ElInput
                    v-model="formData.resumeBase.currentCompany"
                    :disabled="true" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="当前职位">
                  <ElInput
                    v-model="formData.resumeBase.currentPosition"
                    :disabled="true" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="职称级别">
                  <ElInput v-model="formData.resumeBase.professionalTitle" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="应聘职位">
                  <ElInput v-model="formData.resumeBase.desiredPosition" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="期望薪资">
                  <ElInput v-model="formData.resumeBase.expectedSalary">
                    <template #append>元</template>
                  </ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem>
                  <template #label>
                    <span class="mr-[4px] min-w-[84px]">期望工作地点</span>
                    <i class="iconfont icon-info-filled text-[12px]!" />
                  </template>
                  <ElInput v-model="formData.resumeBase.expectLocation" />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>
        </template>
        <!-- 联系方式 -->
        <template v-if="true">
          <label id="label_2">联系方式</label>
          <!-- Preview -->
          <div
            v-show="isPreview"
            class="is_preview">
            <ElRow :gutter="32">
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.phoneNumber)"
                :span="12">
                <LabelValue label="手机号">
                  {{ formData.resumeBase.phoneNumber }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.email)"
                :span="12">
                <LabelValue label="邮箱">
                  {{ formData.resumeBase.email }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.wechat)"
                :span="12">
                <LabelValue label="微信">
                  {{ formData.resumeBase.wechat }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.qq)"
                :span="12">
                <LabelValue label="QQ">{{ formData.resumeBase.qq }}</LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeBase?.homePhoneNumber)"
                :span="12">
                <LabelValue label="座机号">
                  {{ formData.resumeBase.homePhoneNumber }}
                </LabelValue>
              </ElCol>
            </ElRow>
          </div>
          <!-- Form -->
          <div
            v-show="!isPreview"
            class="is_edit">
            <ElRow :gutter="32">
              <ElCol :span="12">
                <ElFormItem label="手机号">
                  <ElInput v-model="formData.resumeBase.phoneNumber" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="邮箱">
                  <ElInput v-model="formData.resumeBase.email" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="微信">
                  <ElInput v-model="formData.resumeBase.wechat" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="QQ">
                  <ElInput v-model="formData.resumeBase.qq" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="座机号">
                  <ElInput v-model="formData.resumeBase.homePhoneNumber" />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>
        </template>
        <!-- 教育背景 -->
        <template v-if="true">
          <label
            v-if="formData.resumeEduBack.length > 0"
            id="label_3">
            教育背景
          </label>
          <!-- Preview -->
          <div
            v-show="isPreview"
            class="is_preview">
            <div
              v-for="(jybj, index) in formData.resumeEduBack"
              :key="index">
              <ElDivider
                v-show="
                  isNotEmpty(jybj.schoolName) ||
                  isNotEmpty(jybj.startDate) ||
                  isNotEmpty(jybj.endDate) ||
                  isNotEmpty(jybj.country) ||
                  isNotEmpty(jybj.city) ||
                  isNotEmpty(jybj.major) ||
                  isNotEmpty(jybj.degree) ||
                  isNotEmpty(jybj.enrollmentMode) ||
                  isNotEmpty(jybj.educationMode) ||
                  isNotEmpty(jybj.ranking) ||
                  isNotEmpty(jybj.gpa) ||
                  isNotEmpty(jybj.lessons) ||
                  isNotEmpty(jybj.awards) ||
                  isNotEmpty(jybj.researchAchievements)
                "
                content-position="left">
                第{{ index + 1 }}
                段教育背景
              </ElDivider>
              <ElRow :gutter="32">
                <ElCol
                  v-show="isNotEmpty(jybj.schoolName)"
                  :span="12">
                  <LabelValue label="学校名称">
                    {{ jybj.schoolName }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="
                    isNotEmpty(jybj.startDate) && isNotEmpty(jybj.endDate)
                  "
                  :span="12">
                  <LabelValue label="就读年月">
                    {{
                      jybj.startDate &&
                      jybj.endDate &&
                      `${jybj.startDate} - ${jybj.endDate}`
                    }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.country)"
                  :span="12">
                  <LabelValue label="所属国家">{{ jybj.country }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.city)"
                  :span="12">
                  <LabelValue label="城市">{{ jybj.city }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.major)"
                  :span="12">
                  <LabelValue label="专业名称">{{ jybj.major }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.degree)"
                  :span="12">
                  <LabelValue label="学历">
                    {{
                      {
                        '0': '大专以下',
                        '1': '大专',
                        '2': '本科',
                        '3': '硕士',
                        '4': '博士',
                      }[jybj.degree] || '本科'
                    }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.enrollmentMode)"
                  :span="12">
                  <LabelValue label="招生方式">
                    {{ jybj.enrollmentMode }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.educationMode)"
                  :span="12">
                  <LabelValue label="教育形式">
                    {{ jybj.educationMode }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.ranking)"
                  :span="12">
                  <LabelValue label="专业排名">
                    {{ `前 ${jybj.ranking} %` }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.gpa)"
                  :span="12">
                  <LabelValue label="GPA">{{ jybj.gpa }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.lessons)"
                  :span="24">
                  <LabelValue label="主修课程">{{ jybj.lessons }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.awards)"
                  :span="24">
                  <LabelValue label="荣誉奖励">{{ jybj.awards }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(jybj.researchAchievements)"
                  :span="24">
                  <LabelValue label="科研成果">
                    {{ jybj.researchAchievements }}
                  </LabelValue>
                </ElCol>
              </ElRow>
            </div>
          </div>
          <!-- Form -->
          <div
            v-show="!isPreview"
            class="is_edit">
            <div
              v-for="(jybj, index) in formData.resumeEduBack"
              :key="index">
              <ElDivider content-position="left">
                第{{ index + 1 }}
                段教育背景
              </ElDivider>
              <ElRow :gutter="32">
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeEduBack[${index}].schoolName`"
                    label="学校名称">
                    <ElInput v-model="jybj.schoolName" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeEduBack[${index}].jdny`"
                    label="就读年月">
                    <ElDatePicker
                      v-model="jybj.jdny"
                      type="monthrange"
                      range-separator="To"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      value-format="YYYY-MM" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeEduBack[${index}].country`"
                    label="所属国家">
                    <ElSelect v-model="jybj.country" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeEduBack[${index}].city`"
                    label="城市">
                    <ElInput
                      v-model="jybj.city"
                      class="w-full" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeEduBack[${index}].major`"
                    label="专业名称">
                    <ElInput v-model="jybj.major" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeEduBack[${index}].degree`"
                    label="学历">
                    <ElSelect v-model="jybj.degree">
                      <ElOption
                        label="大专以下"
                        value="0" />
                      <ElOption
                        label="大专"
                        value="1" />
                      <ElOption
                        label="本科"
                        value="2" />
                      <ElOption
                        label="硕士"
                        value="3" />
                      <ElOption
                        label="博士"
                        value="4" />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeEduBack[${index}].enrollmentMode`"
                    label="招生方式">
                    <ElSelect v-model="jybj.enrollmentMode">
                      <ElOption
                        label="统招"
                        value="统招" />
                      <ElOption
                        label="非统招"
                        value="非统招" />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeEduBack[${index}].educationMode`"
                    label="教育形式">
                    <ElSelect v-model="jybj.educationMode">
                      <ElOption
                        label="全日制"
                        value="全日制" />
                      <ElOption
                        label="非全日制"
                        value="非全日制" />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeEduBack[${index}].ranking`"
                    label="专业排名">
                    <ElInput v-model="jybj.ranking">
                      <template #prepend>前</template>
                      <template #append>%</template>
                    </ElInput>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`jybj[${index}].gpa`"
                    label="GPA">
                    <ElInput v-model="jybj.gpa" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem :prop="`resumeEduBack[${index}].lessons`">
                    <template #label>
                      <span class="mr-[4px]">主修课程</span>
                      <i class="iconfont icon-info-filled text-[12px]!" />
                    </template>
                    <ElInput
                      v-model="jybj.lessons"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem :prop="`resumeEduBack[${index}].awards`">
                    <template #label>
                      <span class="mr-[4px]">荣誉奖励</span>
                      <i class="iconfont icon-info-filled text-[12px]!" />
                    </template>
                    <ElInput
                      v-model="jybj.awards"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    :prop="`resumeEduBack[${index}].researchAchievements`">
                    <template #label>
                      <span class="mr-[4px]">科研成果</span>
                      <i class="iconfont icon-info-filled text-[12px]!" />
                    </template>
                    <ElInput
                      v-model="jybj.researchAchievements"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </div>
          </div>
        </template>
        <!-- 在校经历 -->
        <template v-if="true">
          <label
            v-if="formData.resumeCampusExpers.length > 0"
            id="label_4">
            在校经历
          </label>
          <!-- Preview -->
          <div
            v-show="isPreview"
            class="is_preview">
            <div
              v-for="(zxjl, index) in formData.resumeCampusExpers"
              :key="index">
              <ElDivider
                v-show="
                  isNotEmpty(zxjl.startDate) ||
                  isNotEmpty(zxjl.endDate) ||
                  isNotEmpty(zxjl.organization) ||
                  isNotEmpty(zxjl.jobName) ||
                  isNotEmpty(zxjl.jobContent)
                "
                content-position="left">
                第{{ index + 1 }}
                段在校经历
              </ElDivider>
              <ElRow :gutter="32">
                <ElCol
                  v-show="isNotEmpty(zxjl.startDate)"
                  :span="12">
                  <LabelValue label="开始时间">{{ zxjl.startDate }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(zxjl.endDate)"
                  :span="12">
                  <LabelValue label="结束时间">{{ zxjl.endDate }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(zxjl.organization)"
                  :span="12">
                  <LabelValue label="所属组织">
                    {{ zxjl.organization }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(zxjl.jobName)"
                  :span="12">
                  <LabelValue label="职务名称">{{ zxjl.jobName }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(zxjl.jobContent)"
                  :span="24">
                  <LabelValue label="职务内容">
                    {{ zxjl.jobContent }}
                  </LabelValue>
                </ElCol>
              </ElRow>
            </div>
          </div>
          <!-- Form -->
          <div
            v-show="!isPreview"
            class="is_edit">
            <div
              v-for="(zxjl, index) in formData.resumeCampusExpers"
              :key="index">
              <ElDivider content-position="left">
                第{{ index + 1 }}
                段在校经历
              </ElDivider>
              <ElRow :gutter="32">
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeCampusExpers[${index}].startDate`"
                    label="开始时间">
                    <ElDatePicker
                      v-model="zxjl.startDate"
                      type="month"
                      value-format="YYYY-MM" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeCampusExpers[${index}].endDate`"
                    label="结束时间">
                    <ElDatePicker
                      v-model="zxjl.endDate"
                      type="month"
                      value-format="YYYY-MM" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeCampusExpers[${index}].organization`"
                    label="所属组织">
                    <ElInput v-model="zxjl.organization" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeCampusExpers[${index}].jobName`"
                    label="职务名称">
                    <ElInput v-model="zxjl.jobName" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    :prop="`resumeCampusExpers[${index}].jobContent`"
                    label="职务内容">
                    <ElInput
                      v-model="zxjl.jobContent"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </div>
          </div>
        </template>
        <!-- 工作经历 -->
        <template v-if="true">
          <label
            v-if="formData.resumeJobExpers.length > 0"
            id="label_5">
            工作经历
          </label>
          <!-- Preview -->
          <div
            v-show="isPreview"
            class="is_preview">
            <div
              v-for="(gzjl, index) in formData.resumeJobExpers"
              :key="index">
              <ElDivider
                v-show="
                  isNotEmpty(gzjl.companyName) ||
                  isNotEmpty(gzjl.startDate) ||
                  isNotEmpty(gzjl.endDate) ||
                  isNotEmpty(gzjl.companyNature) ||
                  isNotEmpty(gzjl.jobTitle) ||
                  isNotEmpty(gzjl.jobType) ||
                  isNotEmpty(gzjl.workPlace) ||
                  isNotEmpty(gzjl.workContent) ||
                  isNotEmpty(gzjl.workPerformance)
                "
                content-position="left">
                第{{ index + 1 }}
                段工作经历
              </ElDivider>
              <ElRow :gutter="32">
                <ElCol
                  v-show="isNotEmpty(gzjl.companyName)"
                  :span="12">
                  <LabelValue label="公司名称">
                    {{ gzjl.companyName }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="
                    isNotEmpty(gzjl.startDate) || isNotEmpty(gzjl.endDate)
                  "
                  :span="12">
                  <LabelValue label="在职时间">
                    {{
                      gzjl.startDate &&
                      gzjl.endDate &&
                      `${gzjl.startDate} - ${gzjl.endDate}`
                    }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(gzjl.companyNature)"
                  :span="12">
                  <LabelValue label="单位性质">
                    {{ gzjl.companyNature }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(gzjl.jobTitle)"
                  :span="12">
                  <LabelValue label="职位名称">{{ gzjl.jobTitle }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(gzjl.jobType)"
                  :span="12">
                  <LabelValue label="工作性质">{{ gzjl.jobType }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(gzjl.workPlace)"
                  :span="24">
                  <LabelValue label="工作地点">{{ gzjl.workPlace }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(gzjl.workContent)"
                  :span="24">
                  <LabelValue label="工作内容">
                    {{ gzjl.workContent }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(gzjl.workPerformance)"
                  :span="24">
                  <LabelValue label="工作业绩">
                    {{ gzjl.workPerformance }}
                  </LabelValue>
                </ElCol>
              </ElRow>
            </div>
          </div>
          <!-- Form -->
          <div
            v-show="!isPreview"
            class="is_edit">
            <div
              v-for="(gzjl, index) in formData.resumeJobExpers"
              :key="index">
              <ElDivider content-position="left">
                第{{ index + 1 }}
                段工作经历
              </ElDivider>
              <ElRow :gutter="32">
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeJobExpers[${index}].companyName`"
                    label="公司名称">
                    <ElInput v-model="gzjl.companyName" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeJobExpers[${index}].zzsj`"
                    label="在职时间">
                    <ElDatePicker
                      v-model="gzjl.zzsj"
                      type="monthrange"
                      range-separator="To"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      value-format="YYYY-MM" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeJobExpers[${index}].companyNature`"
                    label="单位性质">
                    <ElInput v-model="gzjl.companyNature" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeJobExpers[${index}].jobTitle`"
                    label="职位名称">
                    <ElInput v-model="gzjl.jobTitle" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeJobExpers[${index}].jobType`"
                    label="工作性质">
                    <ElSelect v-model="gzjl.jobType">
                      <ElOption
                        label="实习"
                        value="实习" />
                      <ElOption
                        label="兼职"
                        value="兼职" />
                      <ElOption
                        label="全职"
                        value="全职" />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem :prop="`resumeJobExpers[${index}].workPlace`">
                    <template #label>
                      <span class="mr-[4px]">工作地点</span>
                      <i class="iconfont icon-info-filled text-[12px]!" />
                    </template>
                    <ElInput v-model="gzjl.workPlace" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem :prop="`resumeJobExpers[${index}].workContent`">
                    <template #label>
                      <span class="mr-[4px]">工作内容</span>
                      <i class="iconfont icon-info-filled text-[12px]!" />
                    </template>
                    <ElInput
                      v-model="gzjl.workContent"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    :prop="`resumeJobExpers[${index}].workPerformance`">
                    <template #label>
                      <span class="mr-[4px]">工作业绩</span>
                      <i class="iconfont icon-info-filled text-[12px]!" />
                    </template>
                    <ElInput
                      v-model="gzjl.workPerformance"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </div>
          </div>
        </template>
        <!-- 项目经历 -->
        <template v-if="true">
          <label
            v-if="formData.resumeProjectExpers.length > 0"
            id="label_6">
            项目经历
          </label>
          <!-- Preview -->
          <div
            v-show="isPreview"
            class="is_preview">
            <div
              v-for="(xmjl, index) in formData.resumeProjectExpers"
              :key="index">
              <ElDivider
                v-show="
                  isNotEmpty(xmjl.projectName) ||
                  isNotEmpty(xmjl.startDate) ||
                  isNotEmpty(xmjl.endDate) ||
                  isNotEmpty(xmjl.projectRole) ||
                  isNotEmpty(xmjl.companyName) ||
                  isNotEmpty(xmjl.projectStakeholders) ||
                  isNotEmpty(xmjl.teamSize) ||
                  isNotEmpty(xmjl.projectContent) ||
                  isNotEmpty(xmjl.projectResponsibilities) ||
                  isNotEmpty(xmjl.benefits) ||
                  isNotEmpty(xmjl.contributions) ||
                  isNotEmpty(xmjl.researchAchievements)
                "
                content-position="left">
                第{{ index + 1 }}
                段项目经历
              </ElDivider>
              <ElRow :gutter="32">
                <ElCol
                  v-show="isNotEmpty(xmjl.projectName)"
                  :span="12">
                  <LabelValue label="项目名称">
                    {{ xmjl.projectName }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="
                    isNotEmpty(xmjl.projectName) && isNotEmpty(xmjl.endDate)
                  "
                  :span="12">
                  <LabelValue label="起止时间">
                    {{
                      xmjl.startDate &&
                      xmjl.endDate &&
                      `${xmjl.startDate} - ${xmjl.endDate}`
                    }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(xmjl.projectRole)"
                  :span="12">
                  <LabelValue label="项目角色">
                    {{ xmjl.projectRole }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(xmjl.companyName)"
                  :span="12">
                  <LabelValue label="所在单位">
                    {{ xmjl.companyName }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(xmjl.projectStakeholders)"
                  :span="24">
                  <LabelValue label="项目干系人">
                    {{ xmjl.projectStakeholders }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(xmjl.teamSize)"
                  :span="12">
                  <LabelValue label="团队规模">{{ xmjl.teamSize }}</LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(xmjl.projectContent)"
                  :span="24">
                  <LabelValue label="项目介绍">
                    {{ xmjl.projectContent }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(xmjl.projectResponsibilities)"
                  :span="24">
                  <LabelValue label="项目职责">
                    {{ xmjl.projectResponsibilities }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(xmjl.benefits)"
                  :span="24">
                  <LabelValue label="项目收益">
                    {{ xmjl.benefits }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(xmjl.contributions)"
                  :span="24">
                  <LabelValue label="主要贡献">
                    {{ xmjl.contributions }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(xmjl.researchAchievements)"
                  :span="24">
                  <LabelValue label="科研成果">
                    {{ xmjl.researchAchievements }}
                  </LabelValue>
                </ElCol>
              </ElRow>
            </div>
          </div>
          <!-- Form -->
          <div
            v-show="!isPreview"
            class="is_edit">
            <div
              v-for="(xmjl, index) in formData.resumeProjectExpers"
              :key="index">
              <ElDivider content-position="left">
                第{{ index + 1 }}
                段项目经历
              </ElDivider>
              <ElRow :gutter="32">
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeProjectExpers[${index}].projectName`"
                    label="项目名称">
                    <ElInput v-model="xmjl.projectName" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeProjectExpers[${index}].qzsj`"
                    label="起止时间">
                    <ElDatePicker
                      v-model="xmjl.qzsj"
                      type="monthrange"
                      range-separator="To"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      value-format="YYYY-MM" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeProjectExpers[${index}].projectRole`"
                    label="项目角色">
                    <ElInput v-model="xmjl.projectRole" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeProjectExpers[${index}].companyName`"
                    label="所在单位">
                    <ElInput v-model="xmjl.companyName" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    :prop="`resumeProjectExpers[${index}].projectStakeholders`">
                    <template #label>
                      <span class="mr-[4px]">项目干系人</span>
                      <i class="iconfont icon-info-filled text-[12px]!" />
                    </template>
                    <ElInput v-model="xmjl.projectStakeholders" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    label="团队规模"
                    :prop="`resumeProjectExpers[${index}].teamSize`">
                    <ElInput v-model="xmjl.teamSize">
                      <template #append>人</template>
                    </ElInput>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    label="项目介绍"
                    :prop="`resumeProjectExpers[${index}].projectContent`">
                    <ElInput
                      v-model="xmjl.projectContent"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    label="项目职责"
                    :prop="`resumeProjectExpers[${index}].projectResponsibilities`">
                    <ElInput
                      v-model="xmjl.projectResponsibilities"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    label="项目收益"
                    :prop="`resumeProjectExpers[${index}].benefits`">
                    <ElInput
                      v-model="xmjl.benefits"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    label="主要贡献"
                    :prop="`resumeProjectExpers[${index}].contributions`">
                    <ElInput
                      v-model="xmjl.contributions"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    label="科研成果"
                    :prop="`resumeProjectExpers[${index}].researchAchievements`">
                    <ElInput
                      v-model="xmjl.researchAchievements"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </div>
          </div>
        </template>
        <!-- 培训经历 -->
        <template v-if="true">
          <label
            v-if="formData.resumeTrainExpers.length > 0"
            id="label_7">
            培训经历
          </label>
          <!-- Preview -->
          <div
            v-show="isPreview"
            class="is_preview">
            <div
              v-for="(pxjl, index) in formData.resumeTrainExpers"
              :key="index">
              <ElDivider
                v-show="
                  isNotEmpty(pxjl.trainingProject) ||
                  isNotEmpty(pxjl.startDate) ||
                  isNotEmpty(pxjl.endDate) ||
                  isNotEmpty(pxjl.trainingInstitutions) ||
                  isNotEmpty(pxjl.trainingPlace) ||
                  isNotEmpty(pxjl.trainingContent) ||
                  isNotEmpty(pxjl.trainingLessons) ||
                  isNotEmpty(pxjl.trainingAchievements)
                "
                content-position="left">
                第{{ index + 1 }}
                段培训经历
              </ElDivider>
              <ElRow :gutter="32">
                <ElCol
                  v-show="isNotEmpty(pxjl.trainingProject)"
                  :span="12">
                  <LabelValue label="培训名称">
                    {{ pxjl.trainingProject }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="
                    isNotEmpty(pxjl.startDate) && isNotEmpty(pxjl.endDate)
                  "
                  :span="12">
                  <LabelValue label="培训时间">
                    {{
                      pxjl.startDate &&
                      pxjl.endDate &&
                      `${pxjl.startDate}-${pxjl.endDate}`
                    }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(pxjl.trainingInstitutions)"
                  :span="12">
                  <LabelValue label="培训机构">
                    {{ pxjl.trainingInstitutions }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(pxjl.trainingPlace)"
                  :span="12">
                  <LabelValue label="培训地点">
                    {{ pxjl.trainingPlace }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(pxjl.trainingContent)"
                  :span="24">
                  <LabelValue label="培训内容">
                    {{ pxjl.trainingContent }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(pxjl.trainingLessons)"
                  :span="24">
                  <LabelValue label="培训课程">
                    {{ pxjl.trainingLessons }}
                  </LabelValue>
                </ElCol>
                <ElCol
                  v-show="isNotEmpty(pxjl.trainingAchievements)"
                  :span="24">
                  <LabelValue label="培训成果">
                    {{ pxjl.trainingAchievements }}
                  </LabelValue>
                </ElCol>
              </ElRow>
            </div>
          </div>
          <!-- Form -->
          <div
            v-show="!isPreview"
            class="is_edit">
            <div
              v-for="(pxjl, index) in formData.resumeTrainExpers"
              :key="index">
              <ElDivider content-position="left">
                第{{ index + 1 }}
                段培训经历
              </ElDivider>
              <ElRow :gutter="32">
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeTrainExpers[${index}].trainingProject`"
                    label="培训名称">
                    <ElInput v-model="pxjl.trainingProject" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeTrainExpers[${index}].pxsj`"
                    label="在职时间">
                    <ElDatePicker
                      v-model="pxjl.pxsj"
                      type="monthrange"
                      range-separator="To"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      value-format="YYYY-MM" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeTrainExpers[${index}].trainingInstitutions`"
                    label="培训机构">
                    <ElInput v-model="pxjl.trainingInstitutions" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem
                    :prop="`resumeTrainExpers[${index}].trainingPlace`"
                    label="培训地点">
                    <ElInput v-model="pxjl.trainingPlace" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    :prop="`resumeTrainExpers[${index}].trainingContent`"
                    label="培训内容">
                    <ElInput v-model="pxjl.trainingContent" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    label="培训课程"
                    :prop="`resumeTrainExpers[${index}].trainingLessons`">
                    <ElInput
                      v-model="pxjl.trainingLessons"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem
                    label="培训成果"
                    :prop="`resumeTrainExpers[${index}].trainingAchievements`">
                    <ElInput
                      v-model="pxjl.trainingAchievements"
                      type="textarea" />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </div>
          </div>
        </template>
        <!-- 其他信息 -->
        <template v-if="true">
          <label id="label_8">其他信息</label>
          <!-- Preview -->
          <div
            v-show="isPreview"
            class="is_preview">
            <ElRow>
              <ElCol
                v-show="isNotEmpty(formData.resumeOtherInfo?.lessons)"
                :span="24">
                <LabelValue
                  label="主修课程"
                  :label-width="120">
                  {{ formData.resumeOtherInfo.lessons }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="
                  isNotEmpty(formData.resumeOtherInfo?.researchAchievements)
                "
                :span="24">
                <LabelValue
                  label="科研成果"
                  :label-width="120">
                  {{ formData.resumeOtherInfo.researchAchievements }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="
                  isNotEmpty(formData.resumeOtherInfo?.skillsMap?.['入门'])
                "
                :span="24">
                <LabelValue
                  label="专业技能(入门)"
                  :label-width="120">
                  {{ formData.resumeOtherInfo.skillsMap?.['入门'] }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="
                  isNotEmpty(formData.resumeOtherInfo?.skillsMap?.['熟练'])
                "
                :span="24">
                <LabelValue
                  label="专业技能(熟练)"
                  :label-width="120">
                  {{ formData.resumeOtherInfo.skillsMap?.['熟练'] }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="
                  isNotEmpty(formData.resumeOtherInfo?.skillsMap?.['精通'])
                "
                :span="24">
                <LabelValue
                  label="专业技能(精通)"
                  :label-width="120">
                  {{ formData.resumeOtherInfo.skillsMap?.['精通'] }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeOtherInfo?.certificatesList)"
                :span="24">
                <LabelValue
                  label="资格证书"
                  :label-width="120">
                  {{ formData.resumeOtherInfo.certificates }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeOtherInfo?.languages)"
                :span="24">
                <LabelValue
                  label="语言能力"
                  :label-width="120">
                  {{ formData.resumeOtherInfo.languages }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeOtherInfo?.awards)"
                :span="24">
                <LabelValue
                  label="荣誉奖励"
                  :label-width="120">
                  {{ formData.resumeOtherInfo.awards }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeOtherInfo?.interests)"
                :span="24">
                <LabelValue
                  label="兴趣爱好"
                  :label-width="120">
                  {{ formData.resumeOtherInfo.interests }}
                </LabelValue>
              </ElCol>
              <ElCol
                v-show="isNotEmpty(formData.resumeOtherInfo?.selfEvaluation)"
                :span="24">
                <LabelValue
                  label="自我评价"
                  :label-width="120">
                  {{ formData.resumeOtherInfo.selfEvaluation }}
                </LabelValue>
              </ElCol>
            </ElRow>
          </div>
          <!-- Form -->
          <div
            v-show="!isPreview"
            class="is_edit">
            <ElRow>
              <ElCol :span="24">
                <ElFormItem
                  label="主修课程"
                  label-width="120">
                  <ElInput
                    v-model="formData.resumeOtherInfo.lessons"
                    type="textarea" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem
                  label="科研成果"
                  label-width="120">
                  <ElInput
                    v-model="formData.resumeOtherInfo.researchAchievements"
                    :rows="8"
                    type="textarea" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem label-width="120">
                  <template #label>
                    <span class="mr-[2px]">专业技能(入门)</span>
                    <i class="iconfont icon-info-filled text-[12px]!" />
                  </template>
                  <ElInput
                    v-model="formData.resumeOtherInfo.skillsMap.入门"
                    type="textarea" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem label-width="120">
                  <template #label>
                    <span class="mr-[2px]">专业技能(熟练)</span>
                    <i class="iconfont icon-info-filled text-[12px]!" />
                  </template>
                  <ElInput
                    v-model="formData.resumeOtherInfo.skillsMap.熟练"
                    type="textarea" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem label-width="120">
                  <template #label>
                    <span class="mr-[2px]">专业技能(精通)</span>
                    <i class="iconfont icon-info-filled text-[12px]!" />
                  </template>
                  <ElInput
                    v-model="formData.resumeOtherInfo.skillsMap.精通"
                    type="textarea" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem
                  label="资格证书"
                  label-width="120">
                  <template #label>
                    <span class="mr-[2px]">资格证书</span>
                    <i class="iconfont icon-info-filled text-[12px]!" />
                  </template>
                  <ElCascader
                    v-model="formData.resumeOtherInfo.certificatesData"
                    class="w-full"
                    :options="certificateDropdown"
                    :show-all-levels="false"
                    :collapse-tags="true"
                    :collapse-tags-tooltip="true"
                    :max-collapse-tags="5"
                    :clearable="true"
                    :props="{ multiple: true }" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem
                  label="语言能力"
                  label-width="120">
                  <template #label>
                    <span class="mr-[2px]">语言能力</span>
                    <i class="iconfont icon-info-filled text-[12px]!" />
                  </template>
                  <ElInput
                    v-model="formData.resumeOtherInfo.languages"
                    type="textarea" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem
                  label="荣誉奖励"
                  label-width="120">
                  <template #label>
                    <span class="mr-[2px]">荣誉奖励</span>
                    <i class="iconfont icon-info-filled text-[12px]!" />
                  </template>
                  <ElInput
                    v-model="formData.resumeOtherInfo.awards"
                    type="textarea" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem
                  label="兴趣爱好"
                  label-width="120">
                  <template #label>
                    <span class="mr-[2px]">兴趣爱好</span>
                    <i class="iconfont icon-info-filled text-[12px]!" />
                  </template>
                  <ElInput
                    v-model="formData.resumeOtherInfo.interests"
                    type="textarea" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem
                  label="自我评价"
                  label-width="120">
                  <ElInput
                    v-model="formData.resumeOtherInfo.selfEvaluation"
                    :rows="6"
                    type="textarea" />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>
        </template>
      </ElForm>
    </div>
    <div class="main_label toc">
      <a
        href="#label_1"
        class="highlight">
        基本信息
      </a>
      <a href="#label_2">联系方式</a>
      <a href="#label_3">教育背景</a>
      <a href="#label_4">在校经历</a>
      <a href="#label_5">工作经历</a>
      <a href="#label_6">项目经历</a>
      <a href="#label_7">培训经历</a>
      <a href="#label_8">其他信息</a>
    </div>
  </main>
</template>

<script setup>
  import LabelValue from '@/components/label-value/index.vue'
  import { useDictionaryStore } from '@/stores/dictionary'

  import * as apis from '@/apis/resume'
  import { findPath, isNotEmpty } from '@/utils/index'
  const route = useRoute()
  const dictionaryStore = useDictionaryStore()
  const { cityData, nationData, certificateDropdown } =
    storeToRefs(dictionaryStore)
  const getResumeInfo = async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '加载中',
      background: 'rgba(255, 255, 255, 0.7)',
    })
    try {
      const res = await apis.getResumeInfoDetails({
        resumeId: route.query.resumeBid,
      })
      const {
        resumeBase,
        resumeJobExpers,
        resumeOtherInfo,
        resumeCampusExpers,
        resumeEduBack,
        resumeProjectExpers,
        resumeTrainExpers,
      } = res.data.data
      // 基本信息
      formData.value.resumeBase = {
        ...resumeBase,
        birthplaceArr: isNotEmpty(resumeBase?.birthplace)
          ? resumeBase.birthplace.split('-')
          : [],
        registeredResidenceArr: isNotEmpty(resumeBase?.registeredResidence)
          ? resumeBase.registeredResidence.split('-')
          : [],
      }

      // 其他信息
      formData.value.resumeOtherInfo = resumeOtherInfo

      for (const item of (resumeOtherInfo.certificatesList ??= [])) {
        const result = findPath(certificateDropdown.value, item)
        if (result) {
          ;(formData.value.resumeOtherInfo.certificatesData ??= []).push(result)
        }
      }

      // 教育背景
      formData.value.resumeEduBack = resumeEduBack.map((item) => ({
        ...item,
        jdny: [item.startDate, item.endDate],
      }))
      // 工作经历
      formData.value.resumeJobExpers = resumeJobExpers.map((item) => ({
        ...item,
        zzsj: [item.startDate, item.endDate],
      }))
      // 在校经历
      formData.value.resumeCampusExpers = resumeCampusExpers
      // 项目经历
      formData.value.resumeProjectExpers = resumeProjectExpers.map((item) => ({
        ...item,
        qzsj: [item.startDate, item.endDate],
      }))
      // 培训经历
      formData.value.resumeTrainExpers = resumeTrainExpers.map((item) => ({
        ...item,
        pxsj: [item.startDate, item.endDate],
      }))
      nextTick(() => {
        initTags()
      })
    } catch (error) {
      console.log(error)
    } finally {
      loading.close()
    }
  }

  const formData = ref({
    resumeBase: {
      name: '',
      gender: '',
      nationalIdentityNumber: '',
      dateOfBirth: '',
      age: '',
      birthplaceArr: [],
      currentLocation: '',
      registeredResidenceArr: [],
      height: '',
      weight: '',
      ethnic: '',
      maritalStatus: '',
      politicalStatus: '',
      degree: '',
      schoolName: '',
      major: '',
      currentCompany: '',
      currentPosition: '',
      professionalTitle: '',
      desiredPosition: '',
      expectedSalary: '',
      expectLocation: '',
      phoneNumber: '',
      email: '',
      wechat: '',
      qq: '',
      homePhoneNumber: '',
    },
    resumeEduBack: [],
    resumeCampusExpers: [],
    resumeJobExpers: [],
    resumeProjectExpers: [],
    resumeTrainExpers: [],
    resumeOtherInfo: {
      lessons: '',
      researchAchievements: '',
      skillsMap: {
        入门: '',
        熟练: '',
        精通: '',
      },
      certificatesList: '',
      certificatesData: [],
      languages: '',
      awards: '',
      interests: '',
      selfEvaluation: '',
    },
  })
  const loading = ref(false)
  onMounted(() => {
    getResumeInfo()
  })

  const titles = []
  const isPreview = ref(true)

  const initTags = () => {
    const scrollableElement = document.getElementById('main_form')
    scrollableElement.addEventListener('scroll', scrollHandler)
    const links = document.querySelectorAll('a[href^="#"]')
    for (const link of links) {
      link.addEventListener('click', () => {
        highlight(link)
      })
      const url = new URL(link.href)
      const dom = document.querySelector(url.hash)
      if (dom) {
        titles.push(dom)
      }
    }
  }
  const highlight = (id) => {
    document
      .querySelectorAll('a.highlight')
      .forEach((a) => a.classList.remove('highlight'))
    if (id instanceof HTMLElement) {
      id.classList.add('highlight')
      return
    }
    if (id?.startsWith('#')) {
      id = id.substring(1)
    }
    document.querySelector(`a[href="#${id}"]`).classList.add('highlight')
  }
  const debounce = (fn, delay = 100) => {
    let timer = null
    return function (...args) {
      clearTimeout(timer)
      timer = setTimeout(() => {
        fn.apply(this, args)
      }, delay)
    }
  }
  const scrollHandler = debounce(() => {
    const rects = titles.map((title) => title.getBoundingClientRect())
    const range = 300
    for (let i = 0; i < rects.length; i++) {
      const title = titles[i]
      const rect = rects[i]
      if (rect.top >= 0 && rect.top <= range) {
        highlight(title.id)
        break
      }
      if (
        rect.top < 0 &&
        rects[i + 1] &&
        rects[i + 1].top > document.documentElement.clientHeight
      ) {
        highlight(title.id)
        break
      }
    }
  })
  const validateFormData = () => {
    const { promise, resolve, reject } = Promise.withResolvers()
    try {
      // 基本信息
      formData.value.resumeBase = {
        ...formData.value.resumeBase,
        birthplace: formData.value.resumeBase.birthplaceArr.join('-'),
        registeredResidence:
          formData.value.resumeBase.registeredResidenceArr.join('-'),
      }
      // 教育经历
      formData.value.resumeEduBack = formData.value.resumeEduBack.map(
        (item) => {
          const [startDate, endDate] = item.jdny
          return {
            ...item,
            startDate,
            endDate,
          }
        },
      )
      // 工作经历
      formData.value.resumeJobExpers = formData.value.resumeJobExpers.map(
        (item) => {
          const [startDate, endDate] = item.zzsj
          return {
            ...item,
            startDate,
            endDate,
          }
        },
      )
      // 在校经历(字段不需要循环提取日期)
      // 项目经历
      formData.value.resumeProjectExpers =
        formData.value.resumeProjectExpers.map((item) => {
          const [startDate, endDate] = item.qzsj
          return {
            ...item,
            startDate,
            endDate,
          }
        })
      // 培训经历
      formData.value.resumeTrainExpers = formData.value.resumeTrainExpers.map(
        (item) => {
          const [startDate, endDate] = item.pxsj
          return {
            ...item,
            startDate,
            endDate,
          }
        },
      )
      // 筛选资格证书所有子类，方便后续筛选
      const certificatesList =
        (formData.value.resumeOtherInfo.certificatesData ??= []).map(
          (item) => item[item.length - 1],
        )
      formData.value.resumeOtherInfo = {
        ...formData.value.resumeOtherInfo,
        certificatesList,
        certificates: certificatesList.join(';'),
      }
      resolve(formData.value)
    } catch (error) {
      reject(error)
    }
    return promise
  }
  const onSubmit = () => {
    validateFormData()
      .then((res) => {
        loading.value = true
        apis
          .resumeUpdate({ ...res })
          .then(() => {
            ElMessage.success('更新成功')
            isPreview.value = true
          })
          .catch((error) => {
            console.log(error)
          })
          .finally(() => {
            loading.value = false
          })
      })
      .catch((error) => {
        console.log(error)
      })
  }
</script>

<style lang="scss" scoped>
  .check {
    position: fixed;
    top: 92px;
    right: 36px;
  }

  .main {
    display: flex;
    height: calc(100vh - 256px);

    .main_form {
      flex: 1;
      padding-right: 16px;
      overflow: hidden auto;
      scroll-behavior: smooth;

      &::-webkit-scrollbar {
        width: 7px;
        height: 7px;
        background-color: #f5f5f5;
      }

      /* 定义滚动条轨道 内阴影+圆角 */
      &::-webkit-scrollbar-track {
        border-radius: 10px;
        background-color: #f5f5f5;
        box-shadow: inset 0 0 6px rgb(0 0 0 / 30%);
      }

      /* 定义滑块 内阴影+圆角 */
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background-color: #c8c8c8;
        box-shadow: inset 0 0 6px rgb(0 0 0 / 10%);
      }

      label {
        display: block;
        margin-bottom: 20px;
        font-weight: bold;
        font-size: 18px;
      }
    }

    .main_label {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 120px;
      border-left: 1px solid #e6e6e6;

      a {
        height: 20px;
        margin: 5px 0;
        color: #909399;
        font-size: 12px;
        line-height: 20px;
      }

      .highlight {
        color: orange;
      }
    }
  }

  .icon-info-filled {
    display: flex;
    align-items: center;
    line-height: 35px;
  }
</style>
