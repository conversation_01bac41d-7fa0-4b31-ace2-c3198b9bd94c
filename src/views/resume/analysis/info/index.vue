<template>
  <div class="main_page">
    <header>
      <BackTitle
        :is-custom="true"
        @on-back="onBack" />
    </header>
    <main class="main">
      <ResultOriginal />
      <ResumeForm />
    </main>
  </div>
</template>

<script setup>
  import BackTitle from '@/components/back-title/index.vue'
  import ResumeForm from './components/resume-form.vue'
  import ResultOriginal from './components/resume-original.vue'

  const router = useRouter()
  const onBack = () => {
    router.back()
  }
</script>

<style lang="scss" scoped>
  .main_page {
    display: flex;
    row-gap: 15px;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding: 20px;
    background-color: var(--white);

    .main {
      display: flex;

      // margin-top: 20px;
      column-gap: 20px;
      flex: 1;
      width: 100%;
      overflow: auto;
    }
  }
</style>
