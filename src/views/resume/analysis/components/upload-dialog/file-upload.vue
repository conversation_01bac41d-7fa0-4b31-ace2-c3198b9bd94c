<template>
  <div class="w-full">
    <ElUpload
      ref="uploadRef"
      v-model:file-list="fileList"
      v-loading="loading"
      :action="`${apiBaseUrl}/api/v1/file/uploads`"
      :data="{ private: false }"
      :drag="true"
      :limit="1"
      :accept="accept"
      :before-upload="handleBeforeUpload"
      :on-progress="handleProgress"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-exceed="handleExceed">
      <ElIcon class="el-icon--upload"><UploadFilled /></ElIcon>
      <div class="el-upload__text">
        拖拽文件 或
        <em>点击此处上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip">
          上传仅支持扩展名：.pdf .doc .docx .jpg .png .zip
        </div>
      </template>
    </ElUpload>
  </div>
</template>

<script setup>
  import { apiBaseUrl } from '@/utils/config'
  import { UploadFilled } from '@element-plus/icons-vue'

  const fileList = defineModel('fileList', {
    type: Array,
    default: [],
  })

  const { accept, maxSize } = defineProps({
    maxSize: {
      type: Number,
      default: 10,
    },
    accept: {
      type: String,
      default: '.pdf,.doc,.docx,.jpg,.png,.zip',
    },
  })

  const loading = ref(false)

  const uploadRef = useTemplateRef('uploadRef')

  function handleBeforeUpload(file) {
    // 检查文件大小
    if (file.size > maxSize * 1024 * 1024) {
      ElMessage.error(`文件大小不能超过 ${maxSize}MB!`)
      return false
    }

    // 检查文件类型
    if (!accept.split(',').includes('.' + file.name.split('.').pop())) {
      ElMessage.error(`文件类型不支持!`)
      return false
    }

    return true
  }

  function handleProgress() {
    loading.value = true
  }

  function handleSuccess() {
    loading.value = false
  }

  function handleError() {
    loading.value = false
    ElMessage.error(`文件上传失败!`)
  }

  function handleExceed() {
    ElMessage.warning(`当前限制最多上传 1 个文件!`)
  }

  defineExpose({
    clearFiles() {
      uploadRef.value.clearFiles()
    },
  })
</script>

<style lang="scss" scoped></style>
