<template>
  <div class="w-full">
    <ElInput
      :value="inputValue"
      placeholder="请选择职位名称"
      @click="handleEdit" />

    <ElDialog
      v-model="dialogVisible"
      title="选择职位"
      width="1080px"
      :append-to-body="true">
      <ElTable
        :max-height="465"
        :border="true"
        :data="tableData">
        <template #empty>
          <ElEmpty
            :image-size="160"
            description="暂无职位信息" />
        </template>
        <ElTableColumn
          label="职位ID"
          prop="prop_职位ID"
          fixed="left" />
        <ElTableColumn
          label="职位名称"
          prop="prop_职位名称"
          fixed="left" />
        <ElTableColumn
          label="职位类型"
          prop="prop_职位类型">
          <template #default="{ row }">
            {{ getDictLabel('JOB_TYPE', row.prop_职位类型) }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="职位状态"
          prop="prop_职位状态">
          <ElTag type="success">已发布</ElTag>
        </ElTableColumn>
        <ElTableColumn
          label="发布渠道"
          prop="prop_发布渠道">
          <template #default="{ row }">
            <!-- 发布过的职位显示 “Boss直聘” -->
            {{
              row.prop_职位状态 === '0' || row.prop_职位状态 === '1'
                ? 'BOSS直聘'
                : '-'
            }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="关联简历数"
          prop="prop_关联简历数">
          <template #default="{ row }">
            <ElButton
              :link="true"
              type="info">
              {{ row.prop_关联简历数 }}
            </ElButton>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="所属部门"
          prop="prop_所属部门" />
        <ElTableColumn
          label="最新发布时间"
          prop="prop_最新发布时间" />
        <ElTableColumn
          label="操作"
          width="100px"
          fixed="right">
          <template #default="{ row }">
            <ElButton
              :link="true"
              type="primary"
              @click="handleSelect(row)">
              选择
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>

      <div class="mt-[12px] flex items-center justify-end">
        <ElPagination
          v-model:page-size="paginationData.pageSize"
          v-model:current-page="paginationData.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          layout="total,sizes,prev,pager,next,jumper"
          :total="paginationData.total"
          @size-change="handlePageSizeChange"
          @current-change="handlePageNumChange" />
      </div>
    </ElDialog>
  </div>
</template>

<script setup>
  import { getDictLabel } from '@/stores/dict'

  const inputValue = defineModel('inputValue', {
    type: String,
    default: '',
  })

  const dialogVisible = ref(false)
  function handleEdit() {
    dialogVisible.value = true
  }

  const tableData = ref([
    {
      prop_职位ID: '0',
      prop_职位名称: '前端工程师',
      prop_职位类型: '0',
      prop_职位状态: '0',
      prop_关联简历数: 0,
      prop_所属部门: '技术部',
      prop_最新发布时间: '2023-01-01',
    },
  ])
  const paginationData = reactive({
    pageSize: 10,
    pageNum: 1,
    total: 0,
  })

  function handleSelect(row) {
    inputValue.value = row.prop_职位名称
    dialogVisible.value = false
  }

  function onSearch() {
    console.log('search')
  }

  function handlePageSizeChange() {
    paginationData.pageNum = 1
    onSearch()
  }

  function handlePageNumChange() {
    onSearch()
  }
</script>

<style lang="scss" scoped></style>
