<template>
  <ElTabPane
    label="评分权重"
    name="评分权重">
    <ElForm
      v-if="formType"
      ref="formRef"
      label-position="top"
      :model="formData"
      :rules="rulesData">
      <div class="flex flex-wrap content-start gap-[32px]">
        <ElFormItem
          class="flex-1"
          label="教育背景"
          prop="prop_教育背景">
          <ElInputNumber
            v-model="formData.prop_教育背景"
            :step="1"
            :min="0"
            :max="100">
            <template #suffix>
              <span>%</span>
            </template>
          </ElInputNumber>
        </ElFormItem>
        <ElFormItem
          class="flex-1"
          label="工作能力"
          prop="prop_工作能力">
          <ElInputNumber
            v-model="formData.prop_工作能力"
            :step="1"
            :min="0"
            :max="100">
            <template #suffix>
              <span>%</span>
            </template>
          </ElInputNumber>
        </ElFormItem>
        <ElFormItem
          class="flex-1"
          label="语言能力"
          prop="prop_语言能力">
          <ElInputNumber
            v-model="formData.prop_语言能力"
            :step="1"
            :min="0"
            :max="100">
            <template #suffix>
              <span>%</span>
            </template>
          </ElInputNumber>
        </ElFormItem>
        <ElFormItem
          class="flex-1"
          label="荣誉奖项"
          prop="prop_荣誉奖项">
          <ElInputNumber
            v-model="formData.prop_荣誉奖项"
            :step="1"
            :min="0"
            :max="100">
            <template #suffix>
              <span>%</span>
            </template>
          </ElInputNumber>
        </ElFormItem>
        <ElFormItem
          class="flex-1"
          label="技能证书"
          prop="prop_技能证书">
          <ElInputNumber
            v-model="formData.prop_技能证书"
            :step="1"
            :min="0"
            :max="100">
            <template #suffix>
              <span>%</span>
            </template>
          </ElInputNumber>
        </ElFormItem>
      </div>
    </ElForm>
  </ElTabPane>
</template>

<script setup>
  const formData = defineModel('formData', {
    type: Object,
  })
  const { formType } = defineProps({
    formType: {
      type: String,
      default: '',
      validator: (value) => ['create', 'edit'].includes(value),
    },
  })

  const formRef = useTemplateRef('formRef')
  const rulesData = reactive({
    prop_教育背景: [
      {
        required: true,
        message: '请输入教育背景权重',
        trigger: ['blur', 'change'],
      },
    ],
    prop_工作能力: [
      {
        required: true,
        message: '请输入工作能力权重',
        trigger: ['blur', 'change'],
      },
    ],
    prop_语言能力: [
      {
        required: true,
        message: '请输入语言能力权重',
        trigger: ['blur', 'change'],
      },
    ],
    prop_荣誉奖项: [
      {
        required: true,
        message: '请输入荣誉奖项权重',
        trigger: ['blur', 'change'],
      },
    ],
    prop_技能证书: [
      {
        required: true,
        message: '请输入技能证书权重',
        trigger: ['blur', 'change'],
      },
    ],
  })

  defineExpose({
    validate() {
      return new Promise((resolve, reject) => {
        formRef.value.validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('请先完善评分权重'))
          }
        })
      })
    },
  })
</script>

<style lang="scss" scoped></style>
