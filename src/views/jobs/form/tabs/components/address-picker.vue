<template>
  <div class="w-full">
    <ElInput
      :value="showValue"
      placeholder="请选择工作地点"
      @click="handleEdit" />

    <ElDialog
      v-model="dialogVisible"
      title="请选择工作地点"
      width="680px"
      :append-to-body="true">
      <ElAlert
        title="请填写真实有效地址，若招聘平台查实造假，招聘平台账号将被冻结。"
        type="primary"
        :show-icon="true"
        :closable="false" />
      <div class="mt-[12px]">
        <ElTable
          border
          :data="tableData"
          height="250px">
          <template #empty>
            <ElEmpty
              image-size="80px"
              :image="IconAddress">
              <template #description>
                <p class="mt-[-20px]! leading-[24px]">
                  暂无地址，请添加新地址。
                </p>
              </template>
            </ElEmpty>
          </template>
          <ElTableColumn
            label="工作地点"
            prop="prop_工作地点" />
          <ElTableColumn
            label="操作"
            width="100">
            <template #default="{ row }">
              <ElButton
                link
                type="primary"
                @click="handleSelect(row)">
                选择
              </ElButton>
              <ElButton
                link
                type="primary"
                @click="handleDelete(row)">
                删除
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
      <template #footer>
        <ElButton
          type="primary"
          :plain="true"
          @click="handleAdd">
          <template #icon>
            <i class="iconfont icon-circle-plus1 text-[14px]!" />
          </template>
          添加新地址
        </ElButton>
      </template>
    </ElDialog>

    <ElDialog
      v-model="addDialogVisible"
      title="添加新地址"
      width="450px"
      :append-to-body="true">
      <ElForm
        ref="formRef"
        label-position="top"
        :model="formData"
        :rules="rulesData">
        <ElFormItem
          label="工作城市"
          prop="prop_工作城市">
          <ElSelect v-model="formData.prop_工作城市">
            <!-- TODO: -->
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          label="办公大楼"
          prop="prop_办公大楼">
          <ElSelect v-model="formData.prop_办公大楼">
            <!-- TODO: -->
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          label="详细地址"
          prop="prop_详细地址">
          <ElInput
            v-model="formData.prop_详细地址"
            placeholder="请输入工作地点" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleConfirm">
          确定
        </ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup>
  import IconAddress from '@/assets/images/icon-address.png'

  const model = defineModel({
    type: Object,
  })

  const showValue = computed(() => {
    return model.value.prop_工作地点 || ''
  })

  const dialogVisible = ref(false)
  function handleEdit() {
    console.log('edit')
    dialogVisible.value = true
  }

  const tableData = ref([])
  function handleSelect(row) {
    //  TODO:
    console.log('select', row)
    dialogVisible.value = false
  }

  function handleDelete(row) {
    //  TODO:
    console.log('delete', row)
  }

  function handleAdd() {
    addDialogVisible.value = true
  }

  const addDialogVisible = ref(false)
  const formRef = useTemplateRef('formRef')
  const formData = reactive({
    prop_工作城市: '',
    prop_办公大楼: '',
    prop_详细地址: '',
  })
  const rulesData = reactive({
    prop_工作城市: [
      {
        required: true,
        message: '请选择工作城市',
        trigger: ['blur', 'change'],
      },
    ],
    prop_办公大楼: [
      {
        required: true,
        message: '请选择办公大楼',
        trigger: ['blur', 'change'],
      },
    ],
    prop_详细地址: [
      {
        required: true,
        message: '请输入详细地址',
        trigger: ['blur', 'change'],
      },
    ],
  })

  function handleCancel() {
    addDialogVisible.value = false
    formRef.value.resetFields()
  }
  function handleConfirm() {
    formRef.value.validate((valid) => {
      if (valid) {
        addDialogVisible.value = false
      } else {
        console.log('error submit!!')
        return false
      }
    })
  }
</script>

<style lang="scss" scoped></style>
