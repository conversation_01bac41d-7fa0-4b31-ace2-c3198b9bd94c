<!-- 兼职时间选择器 -->
<template>
  <div class="w-full">
    <ElInput
      :value="showValue"
      @click="handleEdit"></ElInput>

    <ElDialog
      v-model="dialogVisible"
      title="请选择兼职时间"
      width="655px"
      :append-to-body="true">
      <ElForm
        ref="formRef"
        label-position="left"
        label-width="110px"
        :model="formData"
        :rules="rulesData">
        <ElFormItem
          label="工作日期"
          prop="prop_工作日期"
          :required="true">
          <div class="radio_group">
            <div
              v-for="item in workDateOptions"
              :key="item.value"
              class="radio_group_item"
              :class="{
                active: formData.prop_工作日期 === item.value,
              }"
              @click="handleWorkDateClick(item.value)">
              {{ item.label }}
            </div>
            <div v-if="formData.prop_工作日期 === 'custom'">
              <ElDatePicker
                v-model="formData.prop_工作日期_自定义"
                style="width: 280px"
                type="daterange" />
            </div>
            <div
              v-else
              class="radio_group_item"
              :class="{
                active: formData.prop_工作日期 === 'custom',
              }"
              @click="handleWorkDateClick('custom')">
              自定义
            </div>
          </div>
        </ElFormItem>
        <ElFormItem
          label="工作时间段"
          prop="prop_工作时间段">
          <div class="radio_group">
            <div
              v-for="item in workTimeOptions"
              :key="item.value"
              class="radio_group_item"
              :class="{
                active: formData.prop_工作时间段 === item.value,
              }"
              @click="handleWorkTimeClick(item.value)">
              {{ item.label }}
            </div>
          </div>
        </ElFormItem>
        <!-- 每周工作天数 -->
        <ElFormItem
          label="每周工作天数"
          prop="prop_每周工作天数">
          <div class="radio_group">
            <div
              v-for="item in minDaysOptions"
              :key="item.value"
              class="radio_group_item"
              :class="{
                active: formData.prop_每周工作天数 === item.value,
              }"
              @click="handleMinDaysClick(item.value)">
              {{ item.label }}
            </div>
          </div>
        </ElFormItem>
        <!-- 工作时间 -->
        <ElFormItem
          label="工作时间"
          prop="prop_工作时间"
          :required="true">
          <ElRadioGroup v-model="formData.prop_工作时间">
            <ElRadio
              v-for="item in workTimeOptions2"
              :key="item.value"
              :label="item.value">
              {{ item.label }}
            </ElRadio>
          </ElRadioGroup>
          <template v-if="formData.prop_工作时间 === '1'">
            <div class="mt-[12px] mb-[18px] w-full">
              <div
                v-for="(item, index) in formData.prop_工作时间_自定义"
                :key="index"
                class="mb-[8px]">
                <ElTimePicker
                  v-model="formData.prop_工作时间_自定义[index]"
                  style="width: 280px"
                  is-range
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间" />
                <ElButton
                  :circle="true"
                  class="ml-[8px]"
                  @click="handleRemoveWorkTime(index)">
                  <template #icon>
                    <i
                      class="iconfont icon-remove"
                      style="color: #606266; font-size: 12px" />
                  </template>
                </ElButton>
              </div>
              <ElButton
                v-if="formData.prop_工作时间_自定义.length < 3"
                type="primary"
                @click="handleAddWorkTime">
                <template #icon>
                  <i class="iconfont icon-circle-plus1" />
                </template>
                新增
              </ElButton>
            </div>
          </template>
          <template v-if="formData.prop_工作时间 === '2'">
            <div class="radio_group mt-[12px]">
              <div
                v-for="item in workShiftOptions"
                :key="item.value"
                class="radio_group_item"
                :class="{
                  active: formData.prop_工作班次.includes(item.value),
                }"
                @click="handleWorkShiftClick(item.value)">
                {{ item.label }}
              </div>
            </div>
          </template>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleConfirm">
          确定
        </ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup>
  const model = defineModel({
    type: Object,
  })

  const showValue = computed(() => {
    const workDateLabel =
      workDateOptions.find((item) => item.value === model.value.prop_工作日期)
        ?.label || (model.value.prop_工作日期 === 'custom' ? '自定义' : '')

    const workTimeLabel = workTimeOptions.find(
      (item) => item.value === model.value.prop_工作时间段,
    )?.label

    const minDaysLabel = minDaysOptions.find(
      (item) => item.value === model.value.prop_每周工作天数,
    )?.label

    const workTimeLabel2 = workTimeOptions2.find(
      (item) => item.value === model.value.prop_工作时间,
    )?.label

    return workDateLabel && workTimeLabel && minDaysLabel && workTimeLabel2
      ? `工作日期：${workDateLabel}；` +
          `工作时间段：${workTimeLabel}；` +
          `每周工作天数：${minDaysLabel}；` +
          `工作时间：${workTimeLabel2}；`
      : ''
  })

  const dialogVisible = ref(false)

  const formRef = useTemplateRef('formRef')
  const formData = reactive({
    prop_工作日期: '',
    prop_工作日期_自定义: [],
    prop_工作时间段: '',
    prop_每周工作天数: '',
    prop_工作时间: '',
    prop_工作时间_自定义: [],
    prop_工作班次: [],
  })
  const rulesData = reactive({
    prop_工作日期: [
      {
        validator: (rule, value, cb) => {
          console.log(formData.prop_工作日期)
          if (!formData.prop_工作日期) {
            cb(new Error('请选择工作日期'))
          }
          if (
            formData.prop_工作日期 === 'custom' &&
            !formData.prop_工作日期_自定义?.length
          ) {
            cb(new Error('请选择自定义工作日期'))
          } else {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
    prop_工作时间段: [
      {
        required: true,
        message: '请选择工作时间段',
        trigger: ['blur', 'change'],
      },
    ],
    prop_每周工作天数: [
      {
        required: true,
        message: '请选择每周工作天数',
        trigger: ['blur', 'change'],
      },
    ],
    prop_工作时间: [
      {
        validator: (rule, value, cb) => {
          if (!formData.prop_工作时间) {
            cb(new Error('请选择工作时间'))
          }
          if (formData.prop_工作时间 === '1') {
            const validate1 =
              formData.prop_工作时间_自定义.length &&
              formData.prop_工作时间_自定义.every((item) => item.length === 2)

            // 时间段不能重复
            const validate2 = formData.prop_工作时间_自定义.every(
              (item, index) => {
                return !formData.prop_工作时间_自定义
                  .slice(index + 1)
                  .some((item2) => {
                    return (
                      // 当前开始时间在比较的时间段内
                      (item[0] >= item2[0] && item[0] <= item2[1]) ||
                      // 当前结束时间在比较的时间段内
                      (item[1] >= item2[0] && item[1] <= item2[1]) ||
                      // 当前时间段包含比较的时间段
                      (item[0] <= item2[0] && item[1] >= item2[1])
                    )
                  })
              },
            )
            if (!validate1) {
              cb(new Error('请选择自定义工作时间'))
            } else if (!validate2) {
              cb(new Error('自定义工作时间不能重复'))
            } else {
              cb()
            }
          }
          if (formData.prop_工作时间 === '2') {
            if (!formData.prop_工作班次.length) {
              cb(new Error('请选择工作班次'))
            } else {
              cb()
            }
          }
          if (formData.prop_工作时间 === '3') {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
  })

  function handleEdit() {
    dialogVisible.value = true
    for (const key in formData) {
      if (Object.prototype.hasOwnProperty.call(formData, key)) {
        formData[key] = model.value[key]
      }
    }
  }

  const workDateOptions = [
    { label: '1个月', value: '1' },
    { label: '2个月', value: '2' },
    { label: '3个月', value: '3' },
    { label: '4个月', value: '4' },
    { label: '长期兼职', value: '5' },
  ]
  function handleWorkDateClick(value) {
    formData.prop_工作日期 = value
    formRef.value.validateField('prop_工作日期')
  }

  const workTimeOptions = [
    { label: '工作日', value: '1' },
    { label: '周末节假日', value: '2' },
    { label: '全周轮班', value: '3' },
    { label: '按单安排时间', value: '4' },
    { label: '不限时间', value: '5' },
  ]
  function handleWorkTimeClick(value) {
    formData.prop_工作时间段 = value
    formRef.value.validateField('prop_工作时间段')
  }

  const minDaysOptions = [
    { label: '5天及以上', value: '0' },
    { label: '3-4天', value: '1' },
    { label: '2-3天', value: '2' },
    { label: '1-2天', value: '3' },
    { label: '无要求', value: '4' },
  ]
  function handleMinDaysClick(value) {
    formData.prop_每周工作天数 = value
    formRef.value.validateField('prop_每周工作天数')
  }

  const workTimeOptions2 = [
    { label: '自定义', value: '1' },
    { label: '按班次', value: '2' },
    { label: '不限时间', value: '3' },
  ]

  function handleAddWorkTime() {
    if (formData.prop_工作时间_自定义.length >= 3) {
      return
    }
    formData.prop_工作时间_自定义.push([])
    formRef.value.validateField('prop_工作时间')
  }

  function handleRemoveWorkTime(index) {
    formData.prop_工作时间_自定义.splice(index, 1)
    formRef.value.validateField('prop_工作时间')
  }

  const workShiftOptions = [
    { label: '早班', value: '1' },
    { label: '午班', value: '2' },
    { label: '晚班', value: '3' },
    { label: '夜班', value: '4' },
  ]
  // 多选
  function handleWorkShiftClick(value) {
    if (formData.prop_工作班次.includes(value)) {
      formData.prop_工作班次 = formData.prop_工作班次.filter(
        (item) => item !== value,
      )
    } else {
      formData.prop_工作班次.push(value)
    }
    formRef.value.validateField('prop_工作时间')
  }

  function handleCancel() {
    dialogVisible.value = false
  }

  function handleConfirm() {
    formRef.value.validate((valid) => {
      if (valid) {
        for (const key in formData) {
          if (Object.prototype.hasOwnProperty.call(formData, key)) {
            model.value[key] = formData[key]
          }
        }
        dialogVisible.value = false
      } else {
        console.log('error submit!!')
        return false
      }
    })
  }

  defineExpose({
    validate() {
      return new Promise((resolve, reject) => {
        if (!showValue.value) {
          reject(new Error('请先完善兼职时间'))
          return
        }

        formRef.value.validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('请先完善兼职时间'))
          }
        })
      })
    },
  })
</script>

<style lang="scss" scoped>
  .radio_group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    .radio_group_item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 116px;
      height: 32px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #ffffff;
      color: #606266;
      font-size: 14px;
      cursor: pointer;

      &:hover {
        border-color: #c0c4cc;
      }

      &.active {
        border: 1px solid #79bbff;
        background: #0055ff;
        color: #ffffff;
      }
    }
  }
</style>
