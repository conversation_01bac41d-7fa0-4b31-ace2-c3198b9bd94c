<template>
  <div class="mt-[8px]">
    <AiButton @click="onClick">AI 帮写</AiButton>

    <ElDialog
      v-model="dialogVisible"
      title="AI 帮写"
      width="800px"
      :append-to-body="true">
      <ElAlert
        title="AI生成内容支持添加在职位描述中继续修改，或重新生成"
        type="primary"
        :show-icon="true"
        :closable="false" />
      <div class="content">
        <div class="content_tip">
          <img
            class="content_tip_img"
            src="@/assets/images/icon-robot.png"
            alt="" />
          <p
            v-if="loading"
            class="content_tip_text">
            AI正在为你生成职位描述···
          </p>
          <p
            v-else
            class="content_tip_text">
            AI已生成，可添加到职位描述中继续修改，或重新生成
          </p>
        </div>
        <div class="content_main"></div>
        <div class="content_footer">
          <AiButton>重新生成</AiButton>
        </div>
      </div>
      <template #footer>
        <ElButton
          type="primary"
          :plain="true"
          @click="handleConfirm">
          填入内容区
        </ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup>
  import { AiButton } from '@/components'

  const dialogVisible = ref(false)

  function onClick() {
    console.log('click')
    dialogVisible.value = true
  }

  const loading = ref(false)

  const emit = defineEmits(['confirm'])

  function handleConfirm() {
    console.log('confirm')
    dialogVisible.value = false

    emit('confirm')
  }
</script>

<style lang="scss" scoped>
  .content {
    display: flex;
    flex-direction: column;
    height: 370px;
    margin-top: 12px;
    padding: 12px;
    border: 1px solid #f4f4f5;
    border-radius: 4px;

    &_tip {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &_img {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }

      &_text {
        background: linear-gradient(58deg, #1749ff 0%, #00e7ff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-fill-color: transparent;
        font-size: 12px;
        line-height: 20px;
      }
    }

    &_main {
      flex: 1;
      overflow: auto;
      color: #606266;
      font-size: 14px;
      line-height: 22px;
    }

    &_footer {
      display: flex;
      margin-top: 12px;
    }
  }
</style>
