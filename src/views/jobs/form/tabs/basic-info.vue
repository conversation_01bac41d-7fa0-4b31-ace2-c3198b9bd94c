<template>
  <ElTabPane
    class="h-full w-full overflow-auto"
    label="基础信息"
    name="基础信息">
    <div
      v-if="formType"
      class="mx-auto my-[5px] w-[1030px]">
      <ElForm
        ref="formRef"
        label-position="top"
        :model="formData"
        :rules="rulesData">
        <ElFormItem
          label="职位类型"
          prop="prop_职位类型">
          <ElRadioGroup v-model="formData.prop_职位类型">
            <ElRadio
              v-for="item in JOB_TYPE"
              :key="item.value"
              :label="item.value"
              :border="true">
              {{ item.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>
        <ElFormItem
          label="职位名称"
          prop="prop_职位名称">
          <ElSelect
            v-model="formData.prop_职位名称"
            placeholder="请输入职位名称"
            :filterable="true"
            :remote="true"
            :remote-method="getOptions_职位名称">
            <ElOption
              v-for="item in options_职位名称"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          label="职位描述"
          prop="prop_职位描述">
          <ElInput
            v-model="formData.prop_职位描述"
            type="textarea"
            :rows="4"
            placeholder="请勿填写QQ、微信、电话等联系方式及特殊符号、性别歧视词、违反劳动法相关内容"
            :show-word-limit="true"
            :maxlength="5000"
            :autosize="{ minRows: 4, maxRows: 8 }"
            resize="none" />
          <AiGen @confirm="handleAiGenConfirm" />
        </ElFormItem>
        <ElFormItem
          label="职位关键词"
          prop="prop_职位关键词">
          <AiButton
            :disabled="!formData.prop_职位名称 || !formData.prop_职位描述"
            @click="handleGenKeywords">
            生成关键词
          </AiButton>
          <div class="mt-[8px] flex w-full flex-wrap gap-[8px]">
            <ElTag
              v-for="item in formData.prop_职位关键词"
              :key="item"
              :closable="item.type === 'custom'"
              @close="handleCloseTag(item)">
              {{ item.label }}
            </ElTag>
          </div>
        </ElFormItem>
        <ElRow :gutter="32">
          <ElCol :span="12">
            <ElFormItem
              label="经验"
              prop="prop_经验">
              <ElSelect
                v-model="formData.prop_经验"
                placeholder="请选择经验"
                :empty-values="[null, undefined]">
                <!-- 社招、兼职 -->
                <template
                  v-if="
                    formData.prop_职位类型 === '0' ||
                    formData.prop_职位类型 === '2'
                  ">
                  <ElOption
                    label="不限"
                    value="" />
                  <ElOption
                    v-for="item in JOB_YEAR"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </template>
                <!-- 校招、实习 直接显示在校/应届 -->
                <template v-else>
                  <ElOption
                    label="在校/应届"
                    value="" />
                </template>
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem
              label="学历"
              prop="prop_学历">
              <ElSelect
                v-model="formData.prop_学历"
                placeholder="请选择学历"
                :empty-values="[null, undefined]">
                <ElOption
                  label="不限"
                  value="" />
                <ElOption
                  v-for="item in JOB_EDUCATION"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <!-- 招聘类型为兼职时显示 -->
          <ElCol
            v-if="formData.prop_职位类型 === '2'"
            :span="12">
            <ElFormItem
              label="结算方式"
              prop="prop_结算方式">
              <ElSelect v-model="formData.prop_结算方式">
                <ElOption
                  v-for="item in JOB_SETTLEMENT_METHOD"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem
              label="薪资范围"
              prop="prop_薪资范围">
              <div class="flex w-full items-center">
                <ElSelect
                  v-model="formData.prop_薪资范围_min"
                  class="flex-1">
                  <ElOption
                    v-for="item in salaryRange.min"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
                <span class="mx-[8px]">-</span>
                <ElSelect
                  v-model="formData.prop_薪资范围_max"
                  class="flex-1">
                  <ElOption
                    v-for="item in salaryRange.max"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
                <!-- 实习 -->
                <span
                  v-if="formData.prop_职位类型 === '3'"
                  class="ml-[8px] flex-1 text-[14px] text-[#303133]">
                  元/天
                </span>
                <!-- 社招、校招、兼职 -->
                <template v-else>
                  <span class="mx-[8px]">x</span>
                  <ElSelect
                    v-model="formData.prop_薪资范围_unit"
                    class="flex-1">
                    <ElOption
                      v-for="item in salaryRange.unit"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value" />
                  </ElSelect>
                </template>
              </div>
            </ElFormItem>
          </ElCol>
          <!-- 社招且岗位为销售性质 -->
          <ElCol
            v-if="formData.prop_职位类型 === '0'"
            :span="12">
            <ElFormItem
              label="底薪"
              prop="prop_底薪">
              <div class="flex items-center">
                <ElSelect
                  v-model="formData.prop_底薪"
                  style="width: 150px; margin-right: 8px">
                  <ElOption
                    v-for="item in generateBaseSalaryOptions()"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
                <ElRadio :disabled="true">固定</ElRadio>
              </div>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem
              label="工作地点"
              prop="prop_工作地点">
              <AddressPicker v-model="formData" />
            </ElFormItem>
          </ElCol>
          <!-- 校招时显示 -->
          <ElCol
            v-if="formData.prop_职位类型 === '1'"
            :span="12">
            <ElFormItem
              label="毕业时间"
              prop="prop_毕业时间">
              <div class="flex w-full items-center">
                <ElSelect
                  v-model="formData.prop_毕业时间_start"
                  class="flex-1"
                  :empty-values="[null, undefined]">
                  <ElOption
                    label="不限"
                    value="" />
                  <ElOption
                    v-for="item in generateGraduationTimeOptions()"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
                <span class="mx-[8px]">-</span>
                <ElSelect
                  v-model="formData.prop_毕业时间_end"
                  class="flex-1"
                  :empty-values="[null, undefined]">
                  <ElOption
                    v-if="!formData.prop_毕业时间_start"
                    label="不限"
                    value="" />
                  <template v-else>
                    <!-- 选项为选择的开始时间 和 开始时间 + 1 -->
                    <ElOption
                      :label="`${formData.prop_毕业时间_start}年`"
                      :value="formData.prop_毕业时间_start" />
                    <ElOption
                      :label="`${formData.prop_毕业时间_start + 1}年`"
                      :value="formData.prop_毕业时间_start + 1" />
                  </template>
                </ElSelect>
              </div>
            </ElFormItem>
          </ElCol>
          <!-- 实习时显示 -->
          <ElCol
            v-if="formData.prop_职位类型 === '3'"
            :span="12">
            <ElFormItem
              label="实习要求"
              prop="prop_实习要求">
              <div class="flex w-full items-center">
                <ElSelect
                  v-model="formData.prop_最少实习月数"
                  class="flex-1"
                  placeholder="最少实习月数">
                  <ElOption
                    v-for="item in generateMinInternshipOptions()"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
                <span class="mx-[8px]">-</span>
                <ElSelect
                  v-model="formData.prop_周到岗天数"
                  class="flex-1"
                  placeholder="最少周到岗天数">
                  <ElOption
                    v-for="item in generateMinDaysOptions()"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
              </div>
            </ElFormItem>
          </ElCol>
          <!-- 兼职时显示 -->
          <ElCol
            v-if="formData.prop_职位类型 === '2'"
            :span="12">
            <ElFormItem
              label="兼职时间"
              prop="prop_兼职时间">
              <PartTimePicker
                ref="partTimePickerRef"
                v-model="formData" />
            </ElFormItem>
          </ElCol>
          <!-- 校招、兼职时显示 -->
          <ElCol
            v-if="
              formData.prop_职位类型 === '1' || formData.prop_职位类型 === '2'
            "
            :span="12">
            <ElFormItem
              label="招聘截止时间"
              prop="prop_招聘截止时间">
              <ElDatePicker
                v-model="formData.prop_招聘截止时间"
                class="flex-1"
                type="date"
                placeholder="请选择招聘截止时间" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem
              label="所属部门"
              prop="prop_所属部门">
              <ElSelect>
                <!-- TODO:  -->
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </div>
  </ElTabPane>
</template>

<script setup>
  import { AiButton } from '@/components'
  import { useDict } from '@/stores/dict'
  import AddressPicker from './components/address-picker.vue'
  import AiGen from './components/ai-gen.vue'
  import PartTimePicker from './components/part-time-picker.vue'
  import {
    generateBaseSalaryOptions,
    generateGraduationTimeOptions,
    generateMinDaysOptions,
    generateMinInternshipOptions,
    getSalaryRange,
  } from './utils.js'

  const formData = defineModel('formData', {
    type: Object,
  })

  const { formType } = defineProps({
    formType: {
      type: String,
      default: '',
      validator: (value) => ['create', 'edit'].includes(value),
    },
  })

  const { JOB_TYPE, JOB_YEAR, JOB_EDUCATION, JOB_SETTLEMENT_METHOD } = useDict(
    'JOB_TYPE',
    'JOB_YEAR',
    'JOB_EDUCATION',
    'JOB_SETTLEMENT_METHOD',
  )
  const formRef = useTemplateRef('formRef')
  const partTimePickerRef = useTemplateRef('partTimePickerRef')
  const rulesData = reactive({
    prop_职位类型: [
      {
        required: true,
        message: '请选择职位类型',
        trigger: ['blur', 'change'],
      },
    ],
    prop_职位名称: [
      {
        required: true,
        message: '请输入职位名称',
        trigger: ['blur', 'change'],
      },
    ],
    prop_职位描述: [
      {
        required: true,
        message: '请输入职位描述',
        trigger: ['blur', 'change'],
      },
      {
        validator: (rule, value, cb) => {
          // 计算有多少个字符，汉字算2，英文算1
          const length = value.split('').reduce((acc, cur) => {
            return acc + (cur.charCodeAt(0) > 255 ? 2 : 1)
          }, 0)
          if (length < 4) {
            cb(new Error('职位描述不能少于2个汉字或4个英文字符'))
          } else {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
    prop_职位关键词: [
      {
        required: true,
        message: '请输入职位关键词',
        trigger: ['blur', 'change'],
      },
    ],
    prop_结算方式: [
      {
        required: true,
        message: '请选择结算方式',
        trigger: ['blur', 'change'],
      },
    ],
    prop_薪资范围: [
      {
        validator: (rule, value, cb) => {
          if (
            !formData.value.prop_薪资范围_min || // 没选择薪资范围
            !formData.value.prop_薪资范围_max || // 没选择薪资范围
            formData.value.prop_薪资范围_min >
              formData.value.prop_薪资范围_max || // 最低薪资大于最高薪资
            !formData.value.prop_薪资范围_unit // 没选择薪资单位
          ) {
            cb(new Error('请选择薪资范围'))
          } else {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
    prop_底薪: [
      {
        required: true,
        message: '请选择底薪',
        trigger: ['blur', 'change'],
      },
    ],
    prop_工作地点: [
      {
        required: true,
        message: '请选择工作地点',
        trigger: ['blur', 'change'],
      },
    ],
    prop_毕业时间: [
      {
        validator: (rule, value, cb) => {
          // 开始时间大于结束时间
          if (
            formData.value.prop_毕业时间_start >
            formData.value.prop_毕业时间_end
          ) {
            cb(new Error('请选择毕业时间'))
            return
          }
          cb()
        },
        trigger: ['blur', 'change'],
      },
    ],
    prop_实习要求: [
      {
        validator: (rule, value, cb) => {
          if (
            !formData.value.prop_最少实习月数 ||
            !formData.value.prop_周到岗天数
          ) {
            cb(new Error('请选择实习要求'))
          } else {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
    prop_兼职时间: [
      {
        validator: (rule, value, cb) => {
          console.log(partTimePickerRef.value)
          partTimePickerRef.value.validate().then(cb).catch(cb)
        },
        trigger: ['blur', 'change'],
      },
    ],
    prop_招聘截止时间: [
      {
        required: true,
        message: '请选择招聘截止时间',
        trigger: ['blur', 'change'],
      },
    ],
    prop_所属部门: [
      {
        required: true,
        message: '请选择所属部门',
        trigger: ['blur', 'change'],
      },
    ],
  })

  const options_职位名称 = ref([])
  function getOptions_职位名称(query) {
    console.log(query)
  }

  function handleAiGenConfirm() {
    console.log('confirm')
  }

  const options_职位关键词 = ref([
    { label: '关键词1', type: 'select' },
    { label: '关键词2', type: 'custom' },
  ])
  function handleGenKeywords() {
    options_职位关键词.value.forEach((item) => {
      if (!formData.value.prop_职位关键词.find((i) => i.label === item.label)) {
        formData.value.prop_职位关键词.push(item)
      }
    })
  }
  function handleCloseTag(item) {
    formData.value.prop_职位关键词 = formData.value.prop_职位关键词.filter(
      (i) => i.label !== item.label,
    )
  }

  const salaryRange = computed(() => {
    return getSalaryRange(formData.value.prop_职位类型, {
      method: formData.value.prop_结算方式,
      unit: formData.value.prop_薪资范围_unit,
    })
  })

  // 职位类型改变，要重置结算方式、薪资范围
  watch(
    () => formData.value.prop_职位类型,
    () => {
      // 如果是兼职
      if (formData.value.prop_职位类型 === '2') {
        formData.value.prop_结算方式 = JOB_SETTLEMENT_METHOD.value[0]?.value
        formData.value.prop_薪资范围_unit = salaryRange.value.unit[0]?.value
      } else {
        formData.value.prop_结算方式 = ''
        formData.value.prop_薪资范围_unit = ''
      }
      formData.value.prop_薪资范围_min = ''
      formData.value.prop_薪资范围_max = ''
    },
  )

  defineExpose({
    validate() {
      return new Promise((resolve, reject) => {
        formRef.value.validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('请先完善基础信息'))
          }
        })
      })
    },
  })
</script>

<style lang="scss" scoped></style>
