<template>
  <div class="form">
    <PageHeader :content="pageTitle">
      <template #extra>
        <ElButton
          type="primary"
          :plain="true"
          @click="handleSave(false)">
          保存草稿
        </ElButton>
        <ElButton
          type="primary"
          @click="handleSave(true)">
          发布职位
        </ElButton>
      </template>
    </PageHeader>
    <div class="form_content">
      <ElTabs
        v-model="activeTab"
        style="height: 100%"
        type="border-card">
        <BasicInfo
          ref="basicInfoRef"
          v-model:form-data="formData"
          :form-type="formType" />
        <ScoreWeight
          ref="scoreWeightRef"
          v-model:form-data="formData"
          :form-type="formType" />
      </ElTabs>
    </div>
  </div>
</template>

<script setup>
  import { PageHeader } from '@/components'
  import BasicInfo from './tabs/basic-info.vue'
  import ScoreWeight from './tabs/score-weight.vue'

  const route = useRoute()

  const activeTab = ref('基础信息')
  // 表单类型 create/edit
  const formType = computed(() => route.meta.formType)

  const pageTitle = computed(() => {
    return formType.value === 'create' ? '新增职位' : '编辑职位'
  })

  const formData = reactive({
    // 基础信息
    prop_职位类型: '0',
    prop_职位名称: '',
    prop_职位描述: '',
    prop_职位关键词: [],
    prop_经验: '',
    prop_学历: '',
    prop_结算方式: '',
    prop_薪资范围_min: '',
    prop_薪资范围_max: '',
    prop_薪资范围_unit: '',
    prop_底薪: '',
    prop_工作地点: '',
    prop_毕业时间_start: '',
    prop_毕业时间_end: '',
    prop_最少实习月数: '',
    prop_周到岗天数: '',
    prop_工作日期: '',
    prop_工作日期_自定义: [],
    prop_工作时间段: '',
    prop_每周工作天数: '',
    prop_工作时间: '',
    prop_工作时间_自定义: [],
    prop_工作班次: [],
    prop_招聘截止时间: '',
    prop_所属部门: '',
    // 评分权重
    prop_教育背景: 30, // 教育背景
    prop_工作能力: 50, // 工作能力
    prop_语言能力: 5, // 语言能力
    prop_荣誉奖项: 5, // 荣誉奖项
    prop_技能证书: 10, // 技能证书
  })

  const basicInfoRef = useTemplateRef('basicInfoRef')
  const scoreWeightRef = useTemplateRef('scoreWeightRef')

  function handleSave(isPublish) {
    if (isPublish) {
      Promise.all([
        basicInfoRef.value.validate(),
        scoreWeightRef.value.validate(),
      ])
        .then(() => {
          console.log('保存成功')
        })
        .catch((e) => {
          ElMessage.error(e.message)
        })
    } else {
      console.log('保存草稿')
    }
  }
</script>

<style lang="scss" scoped>
  .form {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow: hidden;
    background-color: #fff;

    &_content {
      flex: 1;
      margin-top: 20px;
      overflow: auto;
    }
  }
</style>
