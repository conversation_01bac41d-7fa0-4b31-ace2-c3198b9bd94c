<template>
  <div class="detail">
    <PageHeader content="查看职位">
      <template #extra>
        <!-- 已发布时显示关闭按钮 -->
        <ElButton
          v-if="jobStatus === '0'"
          type="primary"
          @click="handleClose">
          关闭职位
        </ElButton>
        <!-- 已关闭时显示删除、编辑、发布按钮 -->
        <template v-if="jobStatus === '1'">
          <!-- TODO: 没有关联简历时才显示删除\编辑按钮  -->
          <template v-if="true">
            <ElButton
              type="danger"
              :plain="true"
              @click="handleDelete">
              删除
            </ElButton>
            <ElButton
              type="primary"
              :plain="true"
              @click="handleEdit">
              编辑
            </ElButton>
          </template>
          <ElButton
            type="primary"
            @click="handlePublish">
            发布职位
          </ElButton>
        </template>
        <!-- 发布失败时显示删除、编辑按钮 -->
        <template v-if="jobStatus === '3'">
          <ElButton
            type="danger"
            :plain="true"
            @click="handleDelete">
            删除
          </ElButton>
          <ElButton
            type="primary"
            :plain="true"
            @click="handleEdit">
            编辑
          </ElButton>
        </template>
        <!-- 草稿时显示删除、编辑、发布按钮 -->
        <template v-if="jobStatus === '4'">
          <ElButton
            type="danger"
            :plain="true"
            @click="handleDelete">
            删除
          </ElButton>
          <ElButton
            type="primary"
            :plain="true"
            @click="handleEdit">
            编辑
          </ElButton>
          <ElButton
            type="primary"
            @click="handlePublish">
            发布职位
          </ElButton>
        </template>
      </template>
    </PageHeader>
    <div class="detail_content">
      <DescriptionItems
        title="基础信息"
        :items="basicInfoItems"
        :data="detailData" />

      <div class="mt-[40px]">
        <DescriptionItems
          title="评分权重"
          :items="scoreWeightItems"
          :data="detailData" />
      </div>
    </div>

    <div class="detail_watermark">
      <!-- 已发布 -->
      <i
        v-if="jobStatus === '0'"
        class="iconfont icon-Published"
        style="color: #95d475" />
      <!-- 已关闭 -->
      <i
        v-if="jobStatus === '1'"
        class="iconfont icon-CLOSED"
        style="color: #b1b3b8" />
      <!-- 发布失败 -->
      <i
        v-if="jobStatus === '3'"
        class="iconfont icon-a-Publicationfailed"
        style="color: #f89898" />
      <!-- 草稿 -->
      <i
        v-if="jobStatus === '4'"
        class="iconfont icon-draft"
        style="color: #79bbff" />
    </div>
  </div>
</template>

<script setup>
  import { PageHeader } from '@/components'
  import DescriptionItems from './components/description-items.vue'

  const detailData = ref({})

  const jobStatus = computed(() => {
    return detailData.value.prop_职位状态 || '1'
  })

  const basicInfoItems = ref([
    { label: '职业类型', span: 1, type: 'value', value: '' },
    { label: '职位名称', span: 1, type: 'value', value: '' },
    { label: '职位描述', span: 2, type: 'value', value: '' },
    { label: '职位关键词', span: 2, type: 'tags', value: [] },
    { label: '经验', span: 1, type: 'value', value: '' },
    { label: '学历', span: 1, type: 'value', value: '' },
    { label: '结算方式', span: 2, type: 'value', value: '' },
    { label: '薪资范围', span: 1, type: 'value', value: '' },
    { label: '底薪', span: 2, type: 'value', value: '' },
    { label: '工作地点', span: 1, type: 'value', value: '' },
    { label: '毕业时间', span: 2, type: 'value', value: '' },
    { label: '最少实习月数', span: 2, type: 'value', value: '' },
    { label: '周到岗天数', span: 2, type: 'value', value: '' },
    { label: '工作日期', span: 2, type: 'value', value: '' },
    { label: '工作时间段', span: 2, type: 'value', value: '' },
    { label: '每周工作天数', span: 2, type: 'value', value: '' },
    { label: '工作时间', span: 2, type: 'value', value: '' },
    { label: '工作班次', span: 2, type: 'value', value: '' },
    { label: '招聘截止时间', span: 2, type: 'value', value: '' },
    { label: '所属部门', span: 2, type: 'value', value: '' },
  ])
  const scoreWeightItems = ref([
    { label: '教育背景', span: 1, value: '1' },
    { label: '工作能力', span: 1, value: '32' },
    { label: '语言能力', span: 1, value: '4' },
    { label: '荣誉奖项', span: 1, value: '213' },
    { label: '技能证书', span: 1, value: '12312' },
  ])
</script>

<style lang="scss" scoped>
  .detail {
    display: flex;
    position: relative;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow: hidden;
    background-color: #fff;

    &_content {
      flex: 1;
      margin-top: 20px;
      overflow: auto;
    }

    &_watermark {
      display: flex;
      position: absolute;
      top: 78px;
      right: 20px;
      align-items: center;
      justify-content: center;
      width: 160px;
      height: 160px;

      & > i {
        font-size: 160px;
      }
    }
  }
</style>
