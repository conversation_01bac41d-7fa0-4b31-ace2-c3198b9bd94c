<template>
  <ElDescriptions
    :border="true"
    column="2"
    :title="title"
    :label-width="146">
    <ElDescriptionsItem
      v-for="(item, index) in items"
      :key="index"
      :label="item.label"
      :span="item.span">
      <template v-if="item.type === 'value'">
        {{ item.value }}
      </template>
      <template v-else-if="item.type === 'tags'">
        <ElTag
          v-for="tag in item.value"
          :key="tag"
          type="primary">
          {{ tag.label }}
        </ElTag>
      </template>
    </ElDescriptionsItem>
  </ElDescriptions>
</template>

<script setup>
  const { items, title } = defineProps({
    title: {
      type: String,
      default: '',
    },
    items: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
  })
</script>

<style lang="scss" scoped></style>
