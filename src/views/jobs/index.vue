<template>
  <TablePageLayout title="职位管理">
    <template #header-extra>
      <ElButton
        :text="true"
        type="primary"
        @click="handleCreate">
        <template #icon>
          <i class="iconfont icon-circle-plus text-[12px]!" />
        </template>
        <span class="ml-[4px]">发布职位</span>
      </ElButton>
    </template>
    <template #search>
      <ElForm
        ref="searchFormRef"
        :model="searchData"
        :inline="true"
        :label-width="68">
        <ElFormItem
          label="职位名称"
          prop="prop_职位名称">
          <ElInput
            v-model="searchData.prop_职位名称"
            style="width: 306px"
            placeholder="请输入职位名称" />
        </ElFormItem>
        <ElFormItem
          label="职位类型"
          prop="prop_职位类型">
          <ElSelect
            v-model="searchData.prop_职位类型"
            style="width: 306px"
            placeholder="请选择职位类型"
            :empty-values="[null, undefined]"
            :clearable="true">
            <ElOption
              value=""
              label="不限"></ElOption>
            <ElOption
              v-for="item in JOB_TYPE"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          label="职位状态"
          prop="prop_职位状态">
          <ElSelect
            v-model="searchData.prop_职位状态"
            style="width: 306px"
            placeholder="请选择职位状态"
            :empty-values="[null, undefined]"
            :clearable="true">
            <ElOption
              value=""
              label="不限"></ElOption>
            <ElOption
              v-for="item in JOB_STATUS"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton
            type="primary"
            @click="handleSearch">
            查询
          </ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </template>
    <template #table="{ maxHeight }">
      <ElTable
        :max-height="maxHeight"
        :border="true"
        :data="tableData">
        <template #empty>
          <ElEmpty
            :image-size="160"
            description="暂无职位信息" />
        </template>
        <ElTableColumn
          label="职位ID"
          prop="prop_职位ID" />
        <ElTableColumn
          label="职位名称"
          prop="prop_职位名称" />
        <ElTableColumn
          label="职位类型"
          prop="prop_职位类型">
          <template #default="{ row }">
            {{ getDictLabel('JOB_TYPE', row.prop_职位类型) }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="职位状态"
          prop="prop_职位状态">
          <template #default="{ row }">
            <ElTag
              :type="
                {
                  '0': 'success', // 已发布
                  '1': 'info', // 已关闭
                  '2': 'warning', // 发布中
                  '3': 'danger', // 发布失败
                  '4': 'primary', // 草稿
                }[row.prop_职位状态]
              ">
              {{ getDictLabel('JOB_STATUS', row.prop_职位状态) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="发布渠道"
          prop="prop_发布渠道">
          <template #default="{ row }">
            <!-- 发布过的职位显示 “Boss直聘” -->
            {{
              row.prop_职位状态 === '0' || row.prop_职位状态 === '1'
                ? 'BOSS直聘'
                : '-'
            }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="关联简历数"
          prop="prop_关联简历数">
          <template #default="{ row }">
            <ElButton
              v-if="row.prop_关联简历数 > 0"
              :link="true"
              type="primary"
              @click="handleViewResume(row)">
              {{ row.prop_关联简历数 }}
            </ElButton>
            <ElButton
              v-else
              :link="true"
              type="info">
              {{ row.prop_关联简历数 }}
            </ElButton>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="所属部门"
          prop="prop_所属部门" />
        <ElTableColumn
          label="最新发布时间"
          prop="prop_最新发布时间" />
        <ElTableColumn
          label="操作"
          width="220">
          <template #default="{ row }">
            <!-- 发布中时禁用查看 -->
            <ElButton
              :link="true"
              type="primary"
              :disabled="row.prop_职位状态 === '2'"
              @click="handleView(row)">
              查看
            </ElButton>
            <!-- 已发布、已关闭且有简历关联、发布中时禁用编辑 -->
            <ElButton
              :link="true"
              type="primary"
              :disabled="
                row.prop_职位状态 === '0' ||
                (row.prop_职位状态 === '1' && row.prop_关联简历数 > 0) ||
                row.prop_职位状态 === '2'
              "
              @click="handleEdit(row)">
              编辑
            </ElButton>
            <!-- 已关闭、发布失败、草稿时显示发布按钮 -->
            <ElPopconfirm
              v-if="
                row.prop_职位状态 === '1' ||
                row.prop_职位状态 === '3' ||
                row.prop_职位状态 === '4'
              "
              :visible="publishPopConfirmVisible === row.prop_职位ID"
              title="是否确认发布该职位？发布后会同步到 BOSS 直聘上。"
              width="235px"
              @confirm="handlePublishConfirm(row)"
              @cancel="handlePublishCancel(row)">
              <template #reference>
                <ElButton
                  :link="true"
                  type="primary"
                  @click="handlePublish(row)">
                  发布
                </ElButton>
              </template>
            </ElPopconfirm>
            <!-- 已发布时显示关闭按钮 -->
            <ElPopconfirm
              v-if="row.prop_职位状态 === '0'"
              :visible="closePopConfirmVisible === row.prop_职位ID"
              title="是否确认关闭该职位？关闭后该职位将不再接受简历投递。"
              width="235px"
              @confirm="handleCloseConfirm(row)"
              @cancel="handleCloseCancel(row)">
              <template #reference>
                <ElButton
                  :link="true"
                  type="primary"
                  @click="handleClose(row)">
                  关闭
                </ElButton>
              </template>
            </ElPopconfirm>
            <!-- 已发布、已关闭且有简历、发布中时关联时禁用删除 -->
            <ElPopconfirm
              :visible="deletePopConfirmVisible === row.prop_职位ID"
              title="是否确认删除该职位？删除后该职位将不再对外展示。"
              width="235px"
              @confirm="handleDeleteConfirm(row)"
              @cancel="handleDeleteCancel(row)">
              <template #reference>
                <ElButton
                  :link="true"
                  type="danger"
                  :disabled="
                    row.prop_职位状态 === '0' ||
                    (row.prop_职位状态 === '1' && row.prop_关联简历数 > 0) ||
                    row.prop_职位状态 === '2'
                  "
                  @click="handleDelete(row)">
                  删除
                </ElButton>
              </template>
            </ElPopconfirm>
          </template>
        </ElTableColumn>
      </ElTable>
    </template>
    <template #pagination>
      <ElPagination
        v-model:page-size="paginationData.pageSize"
        v-model:current-page="paginationData.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager,next,jumper"
        :total="paginationData.total"
        @size-change="() => onSearch()"
        @current-change="() => onSearch()" />
    </template>
  </TablePageLayout>
</template>

<script setup>
  import { checkBossLogin } from '@/apis/job'
  import { TablePageLayout } from '@/components'
  import { getDictLabel, useDict } from '@/stores/dict'

  const router = useRouter()
  const { JOB_TYPE, JOB_STATUS } = useDict('JOB_TYPE', 'JOB_STATUS')

  function handleCreate() {
    router.push({
      path: '/jobs/create',
    })
  }

  const searchFormRef = useTemplateRef('searchFormRef')
  const searchData = ref({
    prop_职位名称: '',
    prop_职位类型: '',
    prop_职位状态: '',
  })
  function handleSearch() {
    console.log(searchData.value)
  }
  function handleReset() {
    searchFormRef.value.resetFields()
  }

  const tableData = ref([
    {
      prop_职位ID: '0',
      prop_职位名称: '前端工程师',
      prop_职位类型: '0',
      prop_职位状态: '0',
      prop_关联简历数: 0,
      prop_所属部门: '技术部',
      prop_最新发布时间: '2023-01-01',
    },
    {
      prop_职位ID: '1',
      prop_职位名称: '前端工程师',
      prop_职位类型: '1',
      prop_职位状态: '1',
      prop_关联简历数: 10,
      prop_所属部门: '技术部',
      prop_最新发布时间: '2023-01-01',
    },
    {
      prop_职位ID: '2',
      prop_职位名称: '后端工程师',
      prop_职位类型: '2',
      prop_职位状态: '1',
      prop_关联简历数: 0,
      prop_所属部门: '技术部',
      prop_最新发布时间: '2023-01-01',
    },
    {
      prop_职位ID: '3',
      prop_职位名称: '测试工程师',
      prop_职位类型: '3',
      prop_职位状态: '2',
      prop_关联简历数: 0,
      prop_所属部门: '技术部',
      prop_最新发布时间: '2023-01-01',
    },
    {
      prop_职位ID: '4',
      prop_职位名称: '测试工程师',
      prop_职位类型: '3',
      prop_职位状态: '3',
      prop_关联简历数: 0,
      prop_所属部门: '技术部',
      prop_最新发布时间: '2023-01-01',
    },
    {
      prop_职位ID: '5',
      prop_职位名称: '测试工程师',
      prop_职位类型: '3',
      prop_职位状态: '4',
      prop_关联简历数: 0,
      prop_所属部门: '技术部',
      prop_最新发布时间: '2023-01-01',
    },
  ])

  // 检查 BOSS 直聘是否为登录状态
  function checkLogin() {
    return new Promise((resolve, reject) => {
      // TODO: 检查 BOSS 直聘是否为登录状态
      checkBossLogin()
        .then((res) => {
          const isBossLogin = res.isLogin || true

          if (!isBossLogin) {
            ElMessageBox({
              title: '登录提醒',
              message: h('p', null, [
                h('span', null, '系统监测到用户当前未在'),
                h('span', { class: 'text-[#409EFF]' }, '“渠道管理”'),
                h('span', null, '中登录 BOSS 直聘账号，是否现在前往登录？'),
              ]),
              type: 'warning',
              showCancelButton: true,
              confirmButtonText: '前往登录',
              cancelButtonText: '稍后登录',
              beforeClose: (action, instance, done) => {
                if (action === 'confirm') {
                  router.push({
                    path: '/channel',
                  })
                } else {
                  done()
                }
              },
            })
          }

          resolve(isBossLogin)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  function handleViewResume(row) {
    console.log('查看简历', row)
    // TODO: 跳转到人才画像页面，筛选条件为该职位的关联简历
  }

  function handleView(row) {
    console.log('查看', row)
    router.push({
      path: '/jobs/detail',
      query: {
        jobId: row.prop_职位ID,
      },
    })
  }

  function handleEdit(row) {
    console.log('编辑', row)
    router.push({
      path: '/jobs/edit',
      query: {
        jobId: row.prop_职位ID,
      },
    })
  }

  const publishPopConfirmVisible = ref(null)
  function handlePublish(row) {
    checkLogin()
      .then((isLogin) => {
        if (isLogin) {
          publishPopConfirmVisible.value = row.prop_职位ID
        } else {
          publishPopConfirmVisible.value = null
        }
      })
      .catch((err) => {
        console.log(err)
      })
  }

  function handlePublishConfirm(row) {
    console.log('发布', row)
    publishPopConfirmVisible.value = null
  }

  function handlePublishCancel() {
    publishPopConfirmVisible.value = null
  }

  const closePopConfirmVisible = ref(null)
  function handleClose(row) {
    checkLogin()
      .then((isLogin) => {
        if (isLogin) {
          closePopConfirmVisible.value = row.prop_职位ID
        } else {
          closePopConfirmVisible.value = null
        }
      })
      .catch((err) => {
        console.log(err)
      })
  }

  function handleCloseConfirm(row) {
    console.log('关闭', row)
    closePopConfirmVisible.value = null
  }

  function handleCloseCancel() {
    console.log('取消关闭')
    closePopConfirmVisible.value = null
  }

  const deletePopConfirmVisible = ref(null)
  function handleDelete(row) {
    console.log('删除', row)
    deletePopConfirmVisible.value = row.prop_职位ID
  }

  function handleDeleteConfirm(row) {
    console.log('删除', row)
    deletePopConfirmVisible.value = null
  }

  function handleDeleteCancel() {
    console.log('取消删除')
    deletePopConfirmVisible.value = null
  }

  const paginationData = reactive({
    pageSize: 10,
    pageNum: 1,
    total: 0,
  })
</script>

<style lang="scss" scoped>
  .jobs {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 0 20px;
    overflow: hidden;
    background-color: #fff;

    &_header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 60px;
      border-bottom: 1px solid #dcdfe6;
      &_title {
        color: #303133;
        font-weight: 600;
        font-size: 20px;
      }
    }

    &_search {
      padding: 20px 0;
    }

    &_table {
      flex: 1;
      margin-top: -18px;
      overflow: auto;
    }

    &_pagination {
      display: flex;
      justify-content: flex-end;
      padding: 15px 0;
    }
  }
</style>
