<template>
  <ElSubMenu
    v-if="!isEmpty(item.children)"
    :index="item.key">
    <template #title>
      <i
        class="icon"
        :class="item.icon"></i>
      <span>{{ item.label }}</span>
    </template>
    <MenuItem
      v-for="child in item.children"
      :key="child.id"
      :item="child" />
  </ElSubMenu>

  <ElMenuItem
    v-else
    :index="item.key">
    <i
      class="icon"
      :class="item.icon"></i>
    <span class="title">{{ item.label }}</span>
  </ElMenuItem>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'

  defineProps({
    item: {
      type: Object,
      required: true,
    },
  })
</script>

<style lang="scss" scoped>
  .icon {
    margin-right: 8px;
    text-align: center;
    vertical-align: middle;
  }

  .title {
    height: 40px;
    font-weight: 500;
    line-height: 40px;
  }
</style>
