<template>
  <aside class="sider">
    <ElMenu
      :router="true"
      :collapse="!isLargeScreen"
      :default-openeds="defaultOpenedKeys"
      :default-active="defaultSelectedKey">
      <MenuItem
        v-for="item in menuList"
        :key="item.id"
        :item="item" />
    </ElMenu>
  </aside>
</template>

<script setup>
  import useCommonStore from '@/stores/common'
  import useUserStore from '@/stores/user'
  import { isEmpty } from '@/utils/is'
  import { useMediaQuery } from '@vueuse/core'
  import MenuItem from './menu-item.vue'

  const commonStore = useCommonStore()
  // 当前匹配路由组
  const useStore = useUserStore()

  const menuList = computed(() => {
    return useStore.siderMenu
  })

  // 是否是大屏幕
  const isLargeScreen = useMediaQuery('(min-width: 1000px)')

  // 高亮的菜单
  const defaultSelectedKey = computed(() => {
    const matched = commonStore.matched
    if (!isEmpty(matched)) {
      const { path, meta } = matched[matched.length - 1]

      console.log('meta', meta)
      console.log('path', path)

      return meta?.activeMenu || path
    }

    return ''
  })

  // 展开的菜单
  const defaultOpenedKeys = computed(() => {
    const matched = commonStore.matched

    return matched.map((item) => item.path)
  })

  // 宽度
  const siderWidth = computed(() => {
    return isLargeScreen.value ? '200px' : `${64 + 16}px`
  })
</script>

<style lang="scss" scoped>
  .sider {
    flex: 0 0 v-bind('siderWidth');
    width: v-bind('siderWidth');
    min-width: v-bind('siderWidth');
    max-width: v-bind('siderWidth');
    height: 100%;
    padding: 8px;
    background-color: #fff;
    transition: all 0.5s;

    ul {
      border-right: none;
    }
  }

  :deep(.el-menu-item) {
    height: 40px;
    margin-bottom: 4px;
    border-radius: 2px;
  }

  :deep(.el-sub-menu__title) {
    height: 40px;
    margin-bottom: 4px;
    border-radius: 2px;
  }
</style>
