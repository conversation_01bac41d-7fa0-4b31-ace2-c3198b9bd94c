<template>
  <section class="layout">
    <Header />
    <section class="layout_container">
      <Sider />
      <main class="layout_main">
        <RouterView />
      </main>
    </section>
  </section>
</template>

<script setup>
  import { getUserRole } from '@/apis/sso'
  import { useRoleListStore } from '@/stores/role'
  import { spaceId } from '@/utils/config.js'
  import Header from './header.vue'
  import Sider from './sider/index.vue'

  const roleListStore = useRoleListStore()

  onMounted(() => {
    getRole()
  })

  async function getRole() {
    try {
      const {
        data: { data: res },
      } = await getUserRole({ spaceId })
      roleListStore.userRole = res
    } catch (error) {
      console.log(error)
    }
  }
</script>

<style lang="scss" scoped>
  .layout {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;

    &_container {
      display: flex;
      flex: 1;
      overflow: hidden;
    }

    &_main {
      flex: 1;
      padding: 20px;
      overflow: hidden;
      background-color: #f7f8fa;
    }
  }
</style>
