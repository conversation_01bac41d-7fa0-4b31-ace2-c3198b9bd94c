import {
  apiBaseUrl,
  getToken,
  removeToken,
  removeUserInfo,
  setToken,
  ssoLoginUrl,
} from '@/utils/config'
import { isEmpty } from '@/utils/is'
import axios from 'axios'

// 错误码
const errorCode = {
  401: '认证失败，无法访问系统资源',
  403: '当前操作没有权限',
  404: '访问资源不存在',
  default: '请求服务器失败！',
}

// 根据code检查token是否过期
function checkTokenExpiration(code) {
  if (code === 10126 || code === 10127 || code === 10128 || code === 10117) {
    // token失效
    removeToken()
    // 清空用户信息
    removeUserInfo()
    // 跳转登录
    window.location.href = ssoLoginUrl

    return '未登录，或登录状态已过期，请重新登录！'
  }

  return false
}

// 创建axios实例
const service = axios.create({
  baseURL: apiBaseUrl,
  timeout: 30000,
})

// 拦截发送请求
service.interceptors.request.use(
  (config) => {
    const token = getToken()
    token && (config.headers.Authorization = 'Bearer ' + token)
    return config
  },
  (err) => {
    return Promise.reject(err)
  },
)

// 拦截返回结果
service.interceptors.response.use(
  // 2xx 范围内的状态码都会触发该函数。
  (res) => {
    const { showMsg = true, responseType } = res.config

    if (res.status !== 200) {
      const msg =
        res.data?.msg ||
        errorCode[res.status] ||
        res.statusText ||
        errorCode.default
      showMsg && ElMessage.error(msg)
      return Promise.reject(new Error(msg))
    }

    // 截取新的token并存储
    if (res.headers && res.headers.authorization) {
      const token = res.headers.authorization.replace('Bearer ', '')
      setToken(token)
    }

    // 导出下载文件的情况 返回格式为blob 并且 非JSON格式 (当为blob时，data直接就是一个blob，不会是一个标准接口返回格式)
    if (
      responseType &&
      responseType === 'blob' &&
      !Object.prototype.hasOwnProperty.call(res.data, 'code')
    ) {
      // const contentDisposition = res.headers.get('content-disposition')
      // const fileNameOriginal = contentDisposition
      //   .split(';')[1]
      //   .trim()
      //   .replace(/^filename=/, '')
      // const fileName = fileNameOriginal
      //   ? decodeURIComponent(fileNameOriginal)
      //   : 'download'
      // // 下载文件
      // downloadFileByBlob(res.data, fileName)
      return res
    }

    // 接口code不正确
    if (isEmpty(res.data?.code) || res.data?.code !== 0) {
      // token检查
      const msg = checkTokenExpiration(res.data.code) || res.data.msg

      showMsg && ElMessage.error(msg)
      return Promise.reject(new Error(msg))
    }

    // 最终正确返回
    return res
  },
  // 超出 2xx 范围的状态码都会触发该函数。
  (err) => {
    const { message, response, config } = err
    const { showMsg = true } = config

    let msg = message ? message : '请求服务器失败！'

    // 默认错误处理
    if (message) {
      if (message === 'Network Error') {
        msg = '接口连接异常'
      } else if (message.includes('timeout')) {
        msg = '接口请求超时'
      } else if (message.includes('Request failed with status code')) {
        msg = `后端接口${message.substr(message.length - 3)}异常`
      }
    }

    // 后端返回的详细错误信息
    if (response?.data) {
      if (response.data?.msg) {
        msg = response.data?.msg
      }
      // token验证
      const checkTokenMsg = checkTokenExpiration(response.data?.code)
      if (checkTokenMsg) {
        msg = checkTokenMsg
      }
    }

    showMsg && ElMessage.error(msg)
    err.message = msg
    return Promise.reject(err)
  },
)

/**
 * request封装
 */
export function fetchData(method, url, data = {}, config = { showMsg: true }) {
  return new Promise((resolve, reject) => {
    let request
    if (method === 'get' || method === 'delete') {
      request = service[method](url, { params: data, ...config })
    } else {
      request = service[method](url, data, { ...config })
    }
    request
      .then((res) => {
        resolve(res)
      })
      .catch((e) => {
        reject(e)
      })
  })
}

export function get(url, params, config) {
  return fetchData('get', url, params, config)
}

export function post(url, data, config) {
  return fetchData('post', url, data, config)
}

export function put(url, data, config) {
  return fetchData('put', url, data, config)
}

export function remove(url, params, config) {
  return fetchData('delete', url, params, config)
}

export function upload(url, data, config) {
  const formData = new FormData()
  Object.keys(data).forEach((child) => {
    formData.append(child, data[child])
  })
  return post(url, formData, config)
}

export default service
