import { useRoleListStore } from '@/stores/role'
import { get } from '@/utils/request/axios'
import JSZip from 'jszip'
import JSZipUtils from 'jszip-utils'
/**
 * 数字千分位分割
 * @param { Number } number
 */
export function numberSplit(number) {
  if (number) {
    if (number.toString().includes('.')) {
      return Number(number)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    } else {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    }
  } else {
    return 0
  }
}

// /**
//  * handleBatchDownload函数：批量下载文件并打包成Zip文件
//  * @param {Array} data - 需要下载打包的文件地址数组
//  * @param {string} fileName - 打包文件的默认文件名，默认为'打包下载.zip'
//  * @param {Array} otherFiles - 其他文件数组，每个文件包括fileName和blob属性
//  */
// export const handleBatchDownload = (
//   data,
//   fileName = '打包下载.zip',
//   otherFiles,
// ) => {
//   const zip = new JSZip() // 创建JSZip实例用于操作Zip文件
//   const cache = {} // 缓存已下载文件内容
//   const promises = [] // 用于存储下载文件的Promise数组

//   // 处理传入的文件数据
//   data.forEach((item) => {
//     const promise = getFile(item.url).then((data) => {
//       console.log('data,', data)

//       const fileName = item.fileName
//       zip.file(fileName, data, { binary: true }) // 将文件逐个添加到Zip文件
//       cache[fileName] = data // 缓存文件内容
//     })
//     promises.push(promise)
//   })

//   // 处理其他文件数据
//   if (otherFiles) {
//     otherFiles.forEach((item) => {
//       zip.file(item.fileName, item.blob, { binary: true }) // 将其他文件逐个添加到Zip文件
//       cache[item.fileName] = item.blob // 缓存文件内容
//       promises.push(Promise.resolve(item.blob))
//     })
//   }
//   console.log('promises', promises)

//   // 等待所有文件处理完成
//   // Promise.all(promises).then(() => {
//   //   // 生成Zip文件并保存
//   //   zip.generateAsync({ type: 'blob' }).then((content) => {
//   //     FileSaver.saveAs(content, fileName)
//   //   })
//   // })
// }
// // getFile函数：通过axios从指定URL下载文件并返回Promise
// const getFile = (url) => {
//   return new Promise((resolve, reject) => {
//     axios({
//       method: 'get',
//       url,
//       responseType: 'arraybuffer',
//     })
//       .then((data) => {
//         resolve(data.data)
//       })
//       .catch((error) => {
//         reject(error)
//       })
//   })
// }

export async function getFileUrl(url) {
  if (!url) return
  try {
    const { data: res } = await get(url)
    return res.data
  } catch (e) {
    console.log(e)
  }
}

/**
 * 提取可解析字段
 * @param {*} data
 * @returns
 */
export const extractJsonParsableFields = (data) => {
  if (Array.isArray(data)) {
    return data.map((item) => {
      let parsableFields = {}
      for (let key in item) {
        try {
          // 尝试解析字段
          let parsedValue = JSON.parse(item[key])
          // 如果解析成功，保存到 parsableFields 中
          parsableFields[key] = parsedValue
        } catch (e) {
          // 如果解析失败，跳过该字段
          console.log('error', e)
          continue
        }
      }
      return parsableFields
    })
  } else {
    const parsableFields = {}
    for (let key in data) {
      try {
        // 尝试解析字段
        let parsedValue = JSON.parse(data[key])
        // 如果解析成功，保存到 parsableFields 中
        parsableFields[key] = parsedValue
      } catch (e) {
        // 如果解析失败，跳过该字段
        console.log('error', e)
        continue
      }
    }
    return parsableFields
  }
}

/**
 * 根据权限返回用户是否可用
 * @param {Array|String} roles
 */
export const useRoleControl = (roles) => {
  let isUsable = false

  const roleStore = useRoleListStore()
  const { userRole } = storeToRefs(roleStore)

  if (typeof roles === 'string') {
    userRole.value.forEach((item) => {
      if (item.roleCode === roles) {
        isUsable = true
      }
    })
  } else if (Array.isArray(roles)) {
    userRole.value.forEach((item) => {
      if (roles.includes(item.roleCode)) {
        isUsable = true
      }
    })
  }
  return isUsable
}
/**
 * 判断是否为空
 * @param {*} value
 * @returns
 */
export const isNotEmpty = (value) => {
  return !(
    value === null ||
    value === undefined ||
    value === '' ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === 'object' &&
      !Array.isArray(value) &&
      Object.keys(value).length === 0)
  )
}

/**
 * 批量下载
 * @param {*} fileUrls
 * @param {*} zipFilename
 */
export async function downloadFilesAsZip(
  fileUrls,
  zipFilename = '打包下载.zip',
) {
  const zip = new JSZip()
  const addFileToZip = (url, fileName) => {
    return new Promise((resolve, reject) => {
      JSZipUtils.getBinaryContent(url, (err, data) => {
        if (err) {
          reject(err)
        } else {
          console.log('url', url)
          console.log('fileName', fileName)

          zip.file(fileName, data, { binary: true })
          resolve()
        }
      })
    })
  }

  try {
    await Promise.all(
      fileUrls.map((item) => addFileToZip(item.url, item.fileName)),
    )

    const content = await zip.generateAsync({ type: 'blob' })
    const a = document.createElement('a')
    a.href = URL.createObjectURL(content)
    a.download = zipFilename
    a.click()
    URL.revokeObjectURL(a.href)
  } catch (error) {
    console.error('Error downloading or zipping files:', error)
  }
}

/**
 * 逆向检索对应的路径
 * @param {*} data
 * @param {*} targetValue
 * @returns
 */
export function findPath(data, targetValue) {
  let path = []

  function search(nodes, currentPath) {
    for (let node of nodes) {
      const newPath = [...currentPath, node.label]
      if (node.value === targetValue) path = newPath
      if (node.children) search(node.children, newPath)
    }
  }
  search(data, [])
  if (path.length > 0) return path
}

/**
 * 传入length 根据字符串生产length 长度的Id
 */
export const getUid = (() => {
  const heyStack = '0123456789abcdefghijklmnopqrstuvwxyz'
  const randomInt = () =>
    Math.floor(Math.random() * Math.floor(heyStack.length))

  const id = (length = 24) =>
    Array.from({ length }, () => heyStack[randomInt()]).join('')
  // 因为选择器除开特殊符号,第一个字符必须是字母
  return id
})()
