import { get, post } from '@/utils/request/axios.js'

/**
 * 查询全部字典类型
 * @param {Object} data
 */

export const getDictionary = (data) => post('/api/v1/dictionary/dictType', data)

/**
 * 获取省市区联动数据
 * @param {Object} data
 */

export const getCityData = () => get('/api/v1/resume/info/threeLevelDropdown')

/**
 * 简历解析-民族下拉选
 * @param {Object} data
 */

export const getNation = (data) =>
  get('/api/v1/resume/info/nationDropdown', data)

/**
 * 证书下拉选项
 * @param {Object} data
 */
export const getCertificateDropdown = () =>
  get('/api/v1/resume/info/certificateDropdown')
