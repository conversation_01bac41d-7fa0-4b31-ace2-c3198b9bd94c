import { get, post } from '@/utils/request/axios.js'

// 用户管理

/**
 * 获取账号列表
 */
export const getUserList = (data) => post('/api/v1/user/page/list', data)

/**
 * 获取角色列表
 */
export const getRole = (data) => get('/api/v1/user/all/roles', data)

/**
 * 编辑账号
 */
export const editAccount = (data) => post('/api/v1/user/update', data)

/**
 * 重置密码
 */
export const resetPassword = (data) => post('/api/v1/user/reset', data)

/**
 * 切换账号状态
 */
export const checkoutStatus = (data) =>
  post('/api/v1/user/enable/disable', data)

/**
 * 删除账号
 */
export const removeAccount = (data) => get('/api/v1/user/delete', data)

/**
 * 新增账号
 */
export const addAccount = (data) => post('/api/v1/user/add', data)

/**
 * 批量删除账号
 */
export const batchDeleteAccount = (data) =>
  post('/api/v1/user/delete/batch', data)
