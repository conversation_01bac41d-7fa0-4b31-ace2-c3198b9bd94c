import { get, post } from '@/utils/request/axios.js'

/**
 * 查询二级任务列表
 * @param {Object} data
 */

export const getTaskSubTaskPage = (data) =>
  post('/api/v1/task/subtask/page', data)

/**
 * 查询一级任务列表
 * @param {Object} data
 */

export const getTaskPageList = (data) => post('/api/v1/task/page/list', data)

/**
 * 上传简历解析
 * @param {Object} data
 */

export const uploadTaskAnalysisResume = (data) =>
  post('/api/v1/task/analysis/resume', data)

/**
 * 删除单个任务
 * @param {Object} data
 */

export const deleteTask = (data) => get('/api/v1/task/delete', data)

/**
 * 删除批量任务
 * @param {Object} data
 */

export const deleteBatchTask = (data) => post('/api/v1/task/delete/batch', data)

/**
 * 人才画像-根据bid获取人才基本信息
 * @param {Object} data
 */

export const getTalentDetails = (data) =>
  get('/api/v1/talent/info/details', data)

/**
 * 人才画像-根据bid获取人才标签数据
 * @param {Object} data
 */

export const getTalentTags = (data) => get('/api/v1/talent/info/tags', data)

/**
 * 人才画像-获取列表
 */
export const getTalentList = (data) =>
  post('/api/v1/talent/info/page/list', data)

/**
 * 人才画像-根据bid获取人才简历列表
 * @param {Object} data
 */

export const getTalentResumes = (data) =>
  get('/api/v1/talent/info/resumes', data)

/**
 * 人才画像-根据bid获取人才最新简历
 * @param {Object} data
 */

export const getTalentNewest = (data) => get('/api/v1/talent/info/newest', data)

/**
 * 简历解析-启动任务
 * @param {Object} data
 */

export const getTaskStart = (data) => get('/api/v1/task/start', data)

/**
 * 简历解析-终止任务
 * @param {Object} data
 */

export const getTaskStop = (data) => get('/api/v1/task/stop', data)

/**
 * 简历解析-根据bid获取简历标签数据
 * @param {Object} data
 */

export const getResumeInfoTags = (data) => get('/api/v1/resume/info/tags', data)

/**
 * 简历解析-根据bid获取简历标签数量
 * @param {Object} data
 */

export const getResumeInfoTagNums = (data) =>
  get('/api/v1/resume/info/tagNums', data)

/**
 * 简历解析-根据bid获取简历详情
 * @param {Object} data
 */

export const getResumeInfoDetails = (data) =>
  get('/api/v1/resume/info/details', data)

/**
 * 简历解析-根据bid获取简历评分
 * @param {Object} data
 */

export const getResumeInfoScore = (data) =>
  get('/api/v1/resume/info/getScore', data)

/**
 * 人才画像-根据bid获取人才岗位经验
 * @param {Object} data
 */

export const getResumeInfoExp = (data) =>
  get('/api/v1/talent/info/getExp', data)

/**
 * 人才画像-根据bid获取人才岗位经验
 * @param {Object} data
 */
export const resumeUpdate = (data) => post('/api/v1/resume/info/update', data)

/**
 * 人才画像-根据bid获取人才行业背景
 * @param {Object} data
 */
export const resumeGetIndusty = (data) =>
  get('/api/v1/talent/info/getIndusty', data)

/**
 * 人才画像-根据bid获取人才评分
 * @param {Object} data
 */
export const resumeGetTalentScore = (data) =>
  get('/api/v1/talent/info/score', data)
