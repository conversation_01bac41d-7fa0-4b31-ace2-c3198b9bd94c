import auth from '@/utils/auth'

export default {
  mounted(el, binding) {
    const { value } = binding

    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value

      // 只需要包含其中一个权限即可
      const hasPermissions = auth.hasPermiOr(permissionFlag)

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(
        `请设置操作权限标签值, 参数必须是一个数组且长度大于 0，参考：v-hasPermi="['admin', 'editor']"`,
      )
    }
  },
}
