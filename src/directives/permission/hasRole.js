import auth from '@/utils/auth'

export default {
  mounted(el, binding) {
    const { value } = binding

    if (value && value instanceof Array && value.length > 0) {
      const roleFlag = value
      // 只需要包含其中一个角色
      const hasRole = auth.hasRoleOr(roleFlag)

      if (!hasRole) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置角色权限标签值`)
    }
  },
}
