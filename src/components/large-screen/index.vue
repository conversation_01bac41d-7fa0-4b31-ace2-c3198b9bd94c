<template>
  <div
    id="large_container"
    ref="largeRef">
    <div
      id="large_screen"
      class="large_screen h-full w-full">
      <LargeHeader>
        <!-- 时间 -->
        <TimeNow />
        <!-- 管理后台按钮 -->
        <JumpManage />
      </LargeHeader>
      <LargeMain>
        <template #left>
          <!-- 数据概览 -->
          <LargeOverview :chart-data="overviewData" />
          <!-- 热门行业 -->
          <HotIndustry :chart-data="hotIndustryData" />
          <!-- 薪酬分布 -->
          <PaySpread :chart-data="payData" />
        </template>
        <template #center>
          <!-- 企业-岗位-人才 -->
          <CompanyJobPeople
            :people="peopleCount"
            :job="jobCount"
            :company="enterpriseCount" />
          <!-- 中国地图 -->
          <ChinaMap @map-change="onMapChange" />
          <!-- 热门岗位 -->
          <HotJob :chart-data="hotJobData" />
        </template>
        <template #right>
          <!-- 招聘会 -->
          <JobList />
        </template>
      </LargeMain>
    </div>
  </div>
</template>

<script setup>
  import {
    getGender,
    getPosition,
    getQualification,
    getSalary,
  } from '@/apis/home'
  import { useDict } from '@/stores/dict'
  import autofit from 'autofit.js'
  import screenfull from 'screenfull'
  import ChinaMap from './components/china_map.vue'
  import CompanyJobPeople from './components/company-job-people.vue'
  import HotIndustry from './components/hot-industry.vue'
  import HotJob from './components/hot-job.vue'
  import JobList from './components/job-list.vue'
  import JumpManage from './components/jump-manage.vue'
  import LargeHeader from './components/large-header.vue'
  import LargeMain from './components/large-main.vue'
  import LargeOverview from './components/large-overview.vue'
  import PaySpread from './components/pay-spread.vue'
  import TimeNow from './components/time-now.vue'
  import { mapData } from './data.js'

  const { EDUCATION_LEVEL } = useDict('EDUCATION_LEVEL')

  const largeRef = ref(null)
  onMounted(() => {
    autofit.init({
      dh: 1080,
      dw: 1920,
      el: '#large_screen',
      resize: true,
    })
    screenfull.request(largeRef.value)
  })
  onUnmounted(() => {
    autofit.off()
  })
  const randomData = (list = [], max, min = 0) => {
    let res = [...list]

    res.forEach((i) => {
      if (i.value) {
        let value = Math.floor(Math.random() * max)
        while (value < min) {
          value = Math.floor(Math.random() * max)
        }
        i.value = value
      }
      if (i.children) {
        randomData(i.children, max, min)
      }
    })
    return res
  }

  // 地图层级
  const mapLevel = ref(1)
  const _mapData = ref([])

  const mapParams = ref({ type: '', code: '' })

  const mapTitle = ref('全国')
  function onMapChange(
    addressName,
    level,
    data,
    params = { code: '', type: '' },
  ) {
    mapLevel.value = level
    mapTitle.value = addressName === 'china' ? '全国' : addressName
    _mapData.value = data
    mapParams.value = params
    getQualificationData()
    getPeopleCount()
    getPositionData()
    getEnterpriseCount()
    getJobCount()
    getPayData()
  }

  // 数据概况
  const overviewData = ref([])
  async function getQualificationData() {
    try {
      const {
        data: { data: res },
      } = await getQualification(mapParams.value)
      const result = []
      res.forEach((item) => {
        const name = EDUCATION_LEVEL.value.find(
          (i) => i.value === item.name.toString(),
        )?.label
        if (name === '本科' || name === '硕士' || name === '博士') {
          result.push({
            name,
            value: item.value,
          })
        }
      })
      overviewData.value = result
      console.log('overviewData', result)
    } catch (error) {
      console.log(error)
    }
  }

  // 人才数
  const peopleCount = ref(0)
  async function getPeopleCount() {
    try {
      const {
        data: { data: res },
      } = await getGender(mapParams.value)
      peopleCount.value = res.reduce((total, current) => {
        return total + +current.value
      }, 0)
    } catch (error) {
      console.log(error)
    }
  }

  // 企业数
  const enterpriseCount = ref(0)
  function getEnterpriseCount() {
    const list = [29808, 9808, 3808]
    enterpriseCount.value = list[mapLevel.value - 1]
  }

  // 岗位数
  const jobCount = ref(0)
  function getJobCount() {
    const list = [34704, 4511, 3085]
    jobCount.value = list[mapLevel.value - 1]
  }

  // 热门行业
  const hotIndustryData = computed(() => {
    const res =
      randomData(mapData[0].industry, mapLevel.value ? 89 : 89, 20).sort(
        (a, b) => b.value - a.value,
      ) || []
    console.log('热门行业', res)
    return res
  })

  // 热门岗位
  const hotJobData = ref([])
  async function getPositionData() {
    try {
      const {
        data: { data: res },
      } = await getPosition(mapParams.value)
      hotJobData.value = res.slice(0, 10)
    } catch (error) {
      console.log(error)
    }
  }

  // 薪酬分布
  const payData = computed(() => {
    const res = randomData(mapData[0].pay, 10 ** (6 - mapLevel.value)) || []
    return res
  })
  async function getPayData() {
    try {
      const {
        data: { data: res },
      } = await getSalary(mapParams.value)
      console.log(res)
      // TODO
    } catch (error) {
      console.log(error)
    }
  }
</script>

<style lang="scss" scoped>
  .large_screen {
    display: flex;
    flex-direction: column;
    background-image: url('@/assets/images/large_screen/large_screen_bg.png');
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;

    * {
      user-select: none;
    }
  }
</style>
