<template>
  <div class="hotJob">
    <div class="title">
      <div class="text">热门岗位</div>

      <div class="after">
        <div class="dot bg-[rgba(206,98,28,1)]" />
        <div class="dot bg-[rgba(206,188,28,1)]" />
        <div class="dot bg-[rgba(40,183,220,1)]" />
      </div>
    </div>
    <div class="chartBox">
      <VChart :option />
    </div>
  </div>
</template>

<script setup>
  import { numberSplit } from '@/utils'

  const props = defineProps({
    chartData: {
      type: Array,
      default: () => [],
    },
  })

  const option = computed(() => ({
    color: ['rgba(0, 193, 255, 1)'],
    grid: {
      top: 10,
      left: 0,
      containLabel: true,
      bottom: 0,
      right: 0,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: 'rgba(11, 7, 1, 0.70)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
      },
      formatter(param) {
        return `
        <div class="min-w-[120px]">
          <div>${param[0].name}</div>
          <div class="flex items-center justify-between mt-[10px]">
            <div class="flex items-center">
              <div class="w-[7px] h-[7px] rounded bg-[#00C1FF]"></div>
              <div class="ml-[8px]">人数</div>
            </div>
            <div>
            ${numberSplit(param[0].value)}
            </div>
          </div>
        </div>
        `
      },
    },
    xAxis: {
      type: 'category',
      data: props.chartData.map((item) => item.name),
      axisTick: {
        show: false,
      },
      axisLabel: {
        width: 86,
        overflow: 'truncate',
        interval: 0,
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: 'rgba(72, 72, 73, 1)',
        },
      },
    },
    series: [
      {
        type: 'bar',
        data: props.chartData,
        barWidth: 14,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: 'rgba(0, 193, 255, 0)',
              },
              {
                offset: 0,
                color: 'rgba(0, 193, 255, 1)',
              },
            ],
          },
        },
      },
    ],
  }))
</script>

<style lang="scss" scoped>
  .hotJob {
    // position: absolute;
    // top: 738px;
    // left: 496px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 928px;
      height: 38px;
      padding: 0 18px;
      background-image: url('@/assets/images/large_screen/long-title.png');
      background-size: cover;

      .text {
        color: #fff;
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
      }

      .after {
        display: flex;
        align-items: center;
        gap: 4px;

        .dot {
          width: 6px;
          height: 2px;
        }
      }
    }

    .chartBox {
      width: 100%;
      height: 188px;
      margin-top: 16px;
    }
  }
</style>
