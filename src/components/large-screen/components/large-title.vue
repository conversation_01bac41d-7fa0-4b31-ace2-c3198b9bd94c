<template>
  <div class="container">
    <div class="text">
      {{ titleText }}
    </div>
    <div class="after">
      <slot>
        <div class="dot bg-[rgba(206,98,28,1)]" />
        <div class="dot bg-[rgba(206,188,28,1)]" />
        <div class="dot bg-[rgba(40,183,220,1)]" />
      </slot>
    </div>
  </div>
</template>

<script setup>
  defineProps({
    titleText: {
      type: String,
      default: '',
    },
  })
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 464px;
    height: 38px;
    padding: 0 18px;
    background-image: url('@/assets/images/large_screen/short-title.png');
    background-size: cover;

    .text {
      color: #fff;
      font-weight: 700;
      font-size: 20px;
      line-height: 28px;
    }

    .after {
      display: flex;
      align-items: center;
      gap: 4px;

      .dot {
        width: 6px;
        height: 2px;
      }
    }
  }
</style>
