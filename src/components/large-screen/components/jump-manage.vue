<template>
  <div
    class="button"
    @click="toManage">
    <span class="text">管理后台</span>
    <i class="iconfont icon-dp_home" />
  </div>
</template>

<script setup>
  import { useLargeScreenStore } from '@/stores/large-screen.js'

  const largeScreenStore = useLargeScreenStore()

  function toManage() {
    largeScreenStore.show = false
  }
</script>

<style lang="scss" scoped>
  .button {
    position: absolute;
    top: 42px;
    right: 16px;
    display: flex;
    align-items: center;
    color: #fff;
    cursor: pointer;

    .text {
      color: #fff;
      font-size: 16px;
      line-height: 24px;
    }

    .iconfont {
      margin-left: 6px;
      font-size: 14px;
    }
  }
</style>
