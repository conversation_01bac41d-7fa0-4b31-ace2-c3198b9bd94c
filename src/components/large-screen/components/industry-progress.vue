<template>
  <div class="progressBox">
    <div class="progressTrack">
      <div class="progress">
        <div class="tram" />
      </div>
    </div>
    <div class="percent">{{ percent }}%</div>
  </div>
</template>

<script setup>
  const props = defineProps({
    percent: {
      type: Number,
      default: 0,
    },
  })

  const progressWidth = computed(() => props.percent + '%')
</script>

<style lang="scss" scoped>
  .progressBox {
    display: flex;
    align-items: center;
    height: 22px;

    .progressTrack {
      width: 421px;
      height: 6px;
      background-color: rgb(255 255 255 / 6%);

      .progress {
        position: relative;
        width: v-bind('progressWidth');
        height: 100%;
        background-image: linear-gradient(
          to right,
          rgb(0 193 255 / 0%),
          rgb(0 193 255 / 100%)
        );

        .tram {
          position: absolute;
          top: 50%;
          right: 0;
          width: 2px;
          height: 150%;
          transform: translateY(-50%) skewX(-15deg);
          background-color: #fff;
        }
      }
    }

    .percent {
      flex: 1;
      padding-right: 6px;
      color: #fff;
      font-weight: 700;
      font-size: 14px;
      line-height: 22px;
      text-align: right;
    }
  }
</style>
