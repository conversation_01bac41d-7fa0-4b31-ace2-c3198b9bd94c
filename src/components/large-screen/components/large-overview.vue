<template>
  <div class="overview">
    <LargeTitle title-text="数据概况" />
    <div class="contentBox">
      <div class="valueList">
        <div
          v-for="(item, index) in chartData"
          :key="index"
          class="valueItem">
          <img
            :src="getImgFromName(item.name)"
            alt="" />
          <div class="content">
            <div class="name">{{ item.name }}</div>
            <div class="value">
              {{ numberSplit(item.value) }}
              <span class="unit">人</span>
            </div>
          </div>
        </div>
      </div>
      <div class="chartBox">
        <div class="chart">
          <VChart
            :option
            @mouseover="onMouseOver" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import bkIcon from '@/assets/images/large_screen/bk-icon.png'
  import bsIcon from '@/assets/images/large_screen/bs-icon.png'
  import ssIcon from '@/assets/images/large_screen/ss-icon.png'
  import { numberSplit } from '@/utils'
  import LargeTitle from './large-title.vue'

  const prop = defineProps({
    chartData: {
      type: Array,
      default: () => [],
    },
  })

  function getImgFromName(name) {
    switch (name) {
      case '本科':
        return bkIcon
      case '硕士':
        return ssIcon
      case '博士':
        return bsIcon
    }
  }

  const selectItem = ref('')

  const seriesData = computed(() => {
    if (!selectItem.value) {
      return (
        prop.chartData.map((item, index) => {
          return {
            ...item,
            label:
              index === 0
                ? {
                    show: true,
                  }
                : undefined,
          }
        }) || {}
      )
    } else {
      return (
        prop.chartData.map((item) => {
          return {
            ...item,
            label:
              item.name === selectItem.value
                ? {
                    show: true,
                  }
                : undefined,
          }
        }) || []
      )
    }
  })

  function onMouseOver(e) {
    selectItem.value = e.name
  }

  const option = computed(() => ({
    color: [
      {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: 'rgba(225, 71, 62, 0.05)',
          },
          {
            offset: 1,
            color: 'rgba(225, 71, 62, 1)',
          },
        ],
      },
      {
        type: 'linear',
        x: 1,
        y: 1,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: 'rgba(250, 223, 75, 0.05)',
          },
          {
            offset: 1,
            color: 'rgba(250, 223, 75, 1)',
          },
        ],
      },
      {
        type: 'linear',
        x: 1,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: 'rgba(88, 123, 247, 0.05)',
          },
          {
            offset: 1,
            color: 'rgba(88, 123, 247, 1)',
          },
        ],
      },
    ],
    series: [
      {
        type: 'pie',
        radius: ['73%', '100%'],
        data: seriesData.value || [],
        label: {
          show: false,
          position: 'center',
          fontSize: 16,
          fontWeight: 'bold',
          color: '#fff',
          formatter(param) {
            return `${param.percent}%\n${param.name}占比`
          },
        },
        labelLine: { show: false },
        hoverAnimation: false,
      },
    ],
  }))
</script>

<style lang="scss" scoped>
  .overview {
    width: 464px;
    height: 252px;

    .contentBox {
      display: flex;
      justify-content: space-between;
      padding: 16px;

      .valueList {
        .valueItem {
          display: flex;
          align-items: center;

          img {
            width: 65px;
            height: 50px;
            margin-right: 6px;
          }

          .content {
            .name {
              color: rgb(255 255 255 / 45%);
              font-size: 14px;
              line-height: 22px;
            }

            .value {
              color: #fff;
              font-weight: 600;
              font-size: 20px;
              line-height: 28px;

              .unit {
                font-weight: 400;
                font-size: 14px;
              }
            }
          }

          & + .valueItem {
            margin-top: 10px;
          }
        }
      }

      .chartBox {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 198px;
        height: 198px;
        background-image: url('@/assets/images/large_screen/pie-bg.png');
        background-size: cover;

        .chart {
          width: 162px;
          height: 162px;
        }
      }
    }
  }
</style>
