<template>
  <div class="w-[928px] flex-1">
    <div class="chartContainer h-full w-full">
      <div class="back">
        <p class="back_title">
          人才分布
          <span>({{ title }})</span>
        </p>
        <ElButton
          v-show="mapStack.length > 1"
          link
          type="primary"
          @click="onZoomOutMap">
          返回上一级
        </ElButton>
      </div>
      <VChart
        ref="chartRef"
        :option="option"
        class="map"
        autoresize
        @click="onClickMap" />
      <!-- 人数规模 -->
      <div class="legendBox">
        <div class="legendTitle">人数规模</div>
        <div class="legendContent">
          <div class="legendItem">
            <div class="legend legend1" />
            <div class="legendText">大于1000人</div>
          </div>
          <div class="legendItem">
            <div class="legend legend2" />
            <div class="legendText">501～1000人</div>
          </div>
          <div class="legendItem">
            <div class="legend legend3" />
            <div class="legendText">101～500人</div>
          </div>
          <div class="legendItem">
            <div class="legend legend4" />
            <div class="legendText">1～100人</div>
          </div>
          <div class="legendItem">
            <div class="legend legend5" />
            <div class="legendText">暂无人数</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getHomeMap } from '@/apis/home'
  import chinaJson from '@/assets/json/china.json'
  import { geoApiUrl } from '@/utils/config.js'
  import { numberSplit } from '@/utils/index.js'
  import axios from 'axios'
  import { registerMap } from 'echarts'

  const emits = defineEmits(['mapChange'])
  const title = computed(() => {
    return mapStack.value.at(0).name === 'china'
      ? '中国'
      : mapStack.value.at(0).name
  })
  const mapData = ref([])

  async function getMapData(code = '', type = '') {
    try {
      const {
        data: { data: res },
      } = await getHomeMap({
        code,
        type,
      })
      mapData.value = res
      option.series[0].data = res.map((item) => {
        return {
          ...item,
          emphasis: {
            itemStyle: {
              color: chooseColor(item.value),
            },
            label: {
              show: false,
            },
          },
        }
      })
    } catch (error) {
      console.log(error)
    }
  }

  function getMapApiParams(name, level, isNext = true) {
    if (isNext) {
      const levelMapData = mapData.value.find((item) => item.name === name)
      let code = levelMapData?.code
      let type = null
      if (levelMapData?.isSecond) {
        type = 1
        code += '01'
      } else {
        if (level === 2) {
          type = 0
        } else if (level === 3) {
          type = 1
        }
      }
      return {
        code,
        type,
      }
    } else {
      const code = mapStack.value[1].code
      let type = ''
      if (level === 3) {
        type = 0
      }
      return {
        code,
        type,
      }
    }
  }

  onMounted(async () => {
    await getMapData()
    emits('mapChange', 'china', 1, mapData.value)
  })

  onBeforeMount(() => {
    console.log('option', option.series[0].data)
  })
  registerMap('china', chinaJson)

  const chartRef = ref(null)

  const axiosInstance = axios.create({})

  const mapStack = ref([
    {
      name: 'china',
      json: chinaJson,
      level: '',
      code: '',
    },
  ])

  function onClickMap(params) {
    const { name } = params

    // 演示 仅湖北省和武汉市可点
    // if (name !== '湖北省' && name !== '武汉市') {
    //   return
    // }

    const {
      properties: { adcode, childrenNum, level },
    } = mapStack.value[0].json.features.find((item) => {
      return item.properties.name === name
    })

    const code = mapData.value.find((item) => item.name === name)?.code

    if (childrenNum > 0 && adcode) {
      // 获取下一级的地图 geoJson
      axiosInstance
        .get(`${geoApiUrl}/${adcode}.json`)
        .then(async (res) => {
          if (res.status === 200) {
            const mapJson = res.data
            // 地图入栈
            mapStack.value.unshift({
              name,
              json: mapJson,
              level,
              code,
            })
            registerMap(name, mapJson)
            chartRef.value.clear()
            option.series[0].map = name
            // title.value = name
            const params = getMapApiParams(name, mapStack.value.length)
            await getMapData(params.code, params.type)
            emits(
              'mapChange',
              name,
              mapStack.value.length,
              mapData.value,
              params,
            )
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {})
    }
  }

  // 返回上一级
  async function onZoomOutMap() {
    const parentMap = mapStack.value[1]
    chartRef.value.clear()
    option.series[0].map = parentMap.name
    // title.value = parentMap.name
    const params = getMapApiParams('', mapStack.value.length, false)
    await getMapData(params.code, params.type)
    mapStack.value.shift()
    emits(
      'mapChange',
      parentMap.name,
      mapStack.value.length,
      mapData.value,
      params,
    )
  }

  const chooseColor = (color) => {
    if (color > 1000) return '#E1473E'
    if (color >= 501 && color <= 1000) return '#EF883B'
    if (color >= 101 && color <= 500) return '#FAE04B'
    if (color >= 1 && color <= 100) return '#567AF7'
    if (color === 0) return '#001E3E'
  }

  const option = reactive({
    visualMap: {
      text: ['Max', '0'],
      max: 100,
      show: false,
      type: 'piecewise',
      pieces: [
        {
          min: 1,
          max: 100,
          color: '#8DEEFB',
          colorAlpha: '0.75',
        },
        {
          min: 101,
          max: 500,
          color: '#FCF492',
          colorAlpha: '0.75',
        },
        {
          min: 501,
          max: 1000,
          color: '#F8D290',
          colorAlpha: '0.75',
        },
        {
          min: 1001,
          color: '#DF7E7A',
          colorAlpha: '0.75',
        },
      ],
    },
    tooltip: {
      formatter(param) {
        return `
            <div style="display:flex;align-items:center;">
              <div style="width:6px;height:6px;border-radius:50%;background:#2D5CBC"></div>
              <div style="margin:0 20px 0 8px">${param.name}</div>
              <div>${numberSplit(param.value || 0)}人</div>
            </div>
          `
      },
    },
    animationDurationUpdate: 500,
    series: [
      {
        type: 'map3D',
        map: 'china',
        selectedMode: false,
        data: [],
        itemStyle: {
          color: 'rgba(0, 30, 62, 0.5)',
          opacity: '0.75',
          borderColor: 'rgba(232,243,255,55%)',
          borderWidth: 1,
        },
        emphasis: {
          label: {
            show: false,
          },
        },
      },
    ],
  })
</script>

<style lang="scss" scoped>
  .chartContainer {
    position: relative;

    .back {
      display: flex;
      z-index: 9999;
      position: absolute;
      top: 0;
      right: 0;
      justify-content: space-between;
      width: 100%;

      &_title {
        color: #fff;
        font-size: 20px;

        span {
          margin-left: 10px;
          font-size: 14px;
        }
      }
    }

    .legendBox {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 230px;
      margin-top: 40px;
      color: rgba($color: #fff, $alpha: 50%);

      .legendTitle {
        margin-bottom: 16px;
        color: #fff;
        font-weight: 500;
        font-size: 16px;
      }

      .legendContent {
        display: flex;
        flex-wrap: wrap;

        .legendItem {
          display: flex;
          align-items: center;
          width: 50%;
          margin-bottom: 16px;

          .legend {
            width: 10px;
            height: 10px;
            margin-right: 8px;
            border-radius: 50%;
            box-shadow: 0 0 6px 0 rgb(0 193 255 / 50%);
          }

          .legend1 {
            background: #df7e7a;
          }

          .legend2 {
            background: #ec8e3c;
          }

          .legend3 {
            background: #fcf492;
          }

          .legend4 {
            background: #c3f2ff;
          }

          .legend5 {
            background: #001e3e;
          }

          .legendText {
            // color: rgb(0 0 0 / 65%);
            font-weight: 500;
            font-size: 14px;
          }
        }
      }
    }
  }
</style>
