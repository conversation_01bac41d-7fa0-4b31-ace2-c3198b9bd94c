<template>
  <div class="job">
    <header>
      <div class="job_header">
        <LargeTitle title-text="招聘会" />
        <div class="job_header_title">招聘会数据统计</div>
        <div class="job_header_tags">
          <div class="tag">
            <div class="tag_title">
              <i class="iconfont icon-dp_arrowhead" />
              现场招聘
              <i
                class="iconfont icon-dp_arrowhead"
                style="transform: rotateY(180deg)" />
            </div>
            <div class="tag_logo">
              <i class="iconfont icon-dp_scene text-[36px]" />
            </div>
            <div class="tag_number">
              <p>
                {{ numberSplit(scene) }}
                <span class="ml-[-4px] text-[14px]">场</span>
              </p>
            </div>
          </div>
          <div class="tag">
            <div class="tag_title">
              <i class="iconfont icon-dp_arrowhead" />
              网络招聘
              <i
                class="iconfont icon-dp_arrowhead"
                style="transform: rotateY(180deg)" />
            </div>
            <div class="tag_logo">
              <i class="iconfont icon-dp_Internet text-[36px]" />
            </div>
            <div class="tag_number">
              <p>
                {{ numberSplit(internet) }}
                <span class="ml-[-4px] text-[14px]">场</span>
              </p>
            </div>
          </div>
          <div class="tag">
            <div class="tag_title">
              <i class="iconfont icon-dp_arrowhead" />
              赴外招聘
              <i
                class="iconfont icon-dp_arrowhead"
                style="transform: rotateY(180deg)" />
            </div>
            <div class="tag_logo">
              <i class="iconfont icon-dp_Go-abroad text-[36px]" />
            </div>
            <div class="tag_number">
              <p>
                {{ numberSplit(outside) }}
                <span class="ml-[-4px] text-[14px]">场</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </header>
    <main>
      <div class="main_title">
        <p>招聘会详情</p>
        <div class="function">
          <span
            class="btn"
            :class="{ active: currentType === '现场招聘' }"
            @click="onchange('现场招聘')">
            现场招聘
          </span>
          <div
            class="h-[14px] w-[1px] bg-[rgba(255,255,255,0.15)]"
            style="transform: rotate(15deg)" />
          <span
            class="btn"
            :class="{ active: currentType === '网络招聘' }"
            @click="onchange('网络招聘')">
            网络招聘
          </span>
          <div
            class="h-[14px] w-[1px] bg-[rgba(255,255,255,0.15)]"
            style="transform: rotate(15deg)" />
          <span
            class="btn"
            :class="{ active: currentType === '赴外招聘' }"
            @click="onchange('赴外招聘')">
            赴外招聘
          </span>
        </div>
      </div>
      <div
        id="list"
        ref="listRef"
        class="list">
        <div
          v-for="item in 10"
          :key="item"
          class="item list_item">
          <div class="item_title">
            <img
              src="@/assets/images/large_screen/allow.png"
              alt="" />
            2024夏季招聘会
          </div>
          <div class="item_main">
            <div
              v-if="currentType !== '网络招聘'"
              class="item_main_li">
              <i
                class="iconfont icon-dp_map text-[rgba(255,255,255,0.45);] text-[12px]" />
              <p>
                湖北省武汉市洪山区00{{
                  item + Math.floor(Math.random() * 10)
                }}号
              </p>
            </div>
            <div class="item_main_li">
              <i
                class="iconfont icon-dp_time text-[rgba(255,255,255,0.45);] text-[12px]" />
              <p v-if="currentType !== '网络招聘'">2024-7-17</p>
              <p v-else>
                2024-7-17
                <span class="text-[12px] text-[rgba(255,255,255,0.45)]">
                  至
                </span>
                2024-7-23
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
  import { numberSplit } from '@/utils/index'
  import LargeTitle from './large-title.vue'

  const props = defineProps({
    scene: {
      type: Number,
      default: 236,
    },
    internet: {
      type: Number,
      default: 1482,
    },
    outside: {
      type: Number,
      default: 2849,
    },
  })
  const listRef = ref(null)
  const { scene, internet, outside } = toRefs(props)
  const currentType = ref('现场招聘')
  const onchange = (value) => {
    currentType.value = value
    const list_item = document.getElementsByClassName('list_item')[0]
    if (list_item) {
      list_item.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      })
    }
  }
</script>

<style lang="scss" scoped>
  .job {
    display: flex;
    flex-direction: column;
    width: 464px;
    height: 100%;

    &_header {
      &_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 32px;
        margin-top: 16px;
        padding-right: 16px;
        padding-left: 40px;
        background-image: url('@/assets/images/large_screen/circle-title.png');
        background-size: cover;
        color: #f6f6f6;
        font-size: 18px;
        line-height: 32px;

        .function {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 218px;
          height: 22px;
          font-size: 14px;

          .btn {
            cursor: pointer;
          }

          .active {
            color: #00c1ff;
          }
        }
      }

      &_tags {
        display: flex;
        flex-wrap: nowrap;
        width: 100%;
        height: 218px;
        margin-top: 16px;
        gap: 20px;

        .tag {
          position: relative;
          flex: 1;
          height: 100%;
          padding: 15px 26px;
          background-image: url('@/assets/images/large_screen/transparent.png');
          background-size: cover;

          &_title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 22px;
            color: #fff;
            font-size: 14px;
            text-align: center;

            .icon-dp_arrowhead {
              color: #fff;
              font-size: 6px;
            }
          }

          &_logo {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 88px;
            margin-top: 10px;
            background-image: url('@/assets/images/large_screen/ponk-bg.png');
            background-size: cover;
            color: #fff;
          }

          &_number {
            display: flex;
            position: absolute;
            bottom: 15px;
            left: 50%;
            justify-content: center;
            width: 117px;
            height: 34px;
            transform: translateX(-50%);
            background: rgb(18 26 41 / 40%);
            color: #fff;
            font-size: 20px;
          }
        }
      }
    }

    main {
      display: flex;
      flex: 1;
      flex-direction: column;
      margin-top: 20px;
      overflow: auto;

      .main_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 32px;
        margin-bottom: 12px;
        padding-right: 16px;
        padding-left: 40px;
        background-image: url('@/assets/images/large_screen/circle-title.png');
        background-size: cover;
        color: #f6f6f6;
        font-size: 18px;
        line-height: 32px;

        .function {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 218px;
          height: 22px;
          font-size: 14px;

          .btn {
            cursor: pointer;
          }

          .active {
            color: #00c1ff;
          }
        }
      }

      .list {
        display: flex;
        row-gap: 12px;
        flex: 1;
        flex-direction: column;
        overflow: auto;

        &::-webkit-scrollbar {
          display: none;
        }

        .item {
          width: 100%;

          // min-height: 106px;
          border: 1px solid;
          border-image: linear-gradient(
              270deg,
              rgb(55 114 185 / 100%),
              rgb(55 114 185 / 0%)
            )
            1 1;
          background: rgb(11 41 81 / 30%);

          &_title {
            display: flex;
            column-gap: 6px;
            align-items: center;
            width: 100%;
            height: 36px;
            padding-left: 18px;
            border: 1px solid;
            border-image: linear-gradient(
                270deg,
                rgb(55 114 185 / 100%),
                rgb(55 114 185 / 0%)
              )
              1 1;
            background: rgb(11 41 81 / 50%);
            color: #fff;
            font-weight: bold;
            font-size: 18px;
          }

          &_main {
            padding: 10px 18px;
            color: #fff;
            font-size: 14px;

            &_li {
              display: flex;
              column-gap: 6px;
              align-items: center;
              min-height: 22px;

              p {
                margin-left: 5px;
              }
            }
          }
        }
      }
    }
  }
</style>
