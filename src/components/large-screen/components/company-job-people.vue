<template>
  <div class="company_job_people h-[70px] w-[928px]">
    <div class="header">
      <div
        v-for="item in list"
        :key="item.title"
        class="header_item">
        <img
          :src="item.image"
          class="h-[50px] w-[50px]"
          alt="" />
        <div class="flex flex-1 flex-col justify-between">
          <div class="text-[14px] text-[rgba(255,255,255,0.45)]">
            {{ item.title }}
          </div>
          <p class="text-[20px] text-[#fff]">
            {{ item.value }}
            <span class="ml-[2px] text-[14px]">{{ item.unit }}</span>
          </p>
        </div>
      </div>
    </div>
    <div class="border_line h-[20px] w-full" />
  </div>
</template>

<script setup>
  const props = defineProps({
    company: {
      type: Number,
      default: 0,
    },
    job: {
      type: Number,
      default: 0,
    },
    people: {
      type: Number,
      default: 0,
    },
  })
  const { company, job, people } = toRefs(props)

  // 将数字格式化为千分位
  const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }
  const list = computed(() => {
    return [
      {
        title: '企业数',
        value: formatNumber(company.value),
        unit: '家',
        color: '#20F1FF',
        image: new URL(
          '@/assets/images/large_screen/company_logo.png',
          import.meta.url,
        ).pathname,
      },
      {
        title: '岗位数',
        value: formatNumber(job.value),
        unit: '个',
        color: '#4AFFC9',
        image: new URL(
          '@/assets/images/large_screen/job_logo.png',
          import.meta.url,
        ).pathname,
      },
      {
        title: '人才数',
        value: formatNumber(people.value),
        unit: '人',
        color: '#FFD12E',
        image: new URL(
          '@/assets/images/large_screen/people_logo.png',
          import.meta.url,
        ).pathname,
      },
    ]
  })
</script>

<style lang="scss" scoped>
  .company_job_people {
    // position: absolute;
    // top: 16px;
    // left: 496px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .header {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;

      &_item {
        display: flex;
        position: relative;
        align-items: center;
        height: 50px;
        padding-right: 20px;

        &:not(:first-child) {
          padding-left: 20px;

          &::after {
            position: absolute;
            top: 50%;
            left: 0;
            width: 1px;
            height: 36px;
            transform: translateY(-50%);
            background: linear-gradient(
              180deg,
              rgb(0 193 255 / 0%) 0%,
              rgb(0 193 255 / 60%) 50%,
              rgb(0 193 255 / 0%) 100%
            );
            content: '';
          }
        }
      }
    }

    .border_line {
      background-image: url('@/assets/images/large_screen/border.png');
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
    }
  }
</style>
