<template>
  <div class="main">
    <div class="left">
      <slot name="left" />
    </div>
    <div class="center">
      <slot name="center" />
    </div>
    <div class="right">
      <slot name="right" />
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
  .main {
    position: relative;
    display: flex;
    flex: 1;
    gap: 16px;
    overflow: hidden;
    width: 100%;
    padding: 16px;

    .left,
    .center,
    .right {
      display: flex;
      flex-direction: column;
      gap: 16px;
      height: 100%;
    }

    .left {
      width: 464px;
    }

    .center {
      flex: 1;
    }

    .right {
      width: 464px;
    }
  }
</style>
