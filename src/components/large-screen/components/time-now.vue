<template>
  <div class="timeBox">
    <i class="iconfont icon-dp_time text-[14px] text-[#fff]" />
    <div class="date">{{ date }}</div>
    <div class="time">{{ time }}</div>
    <div class="week">{{ week }}</div>
  </div>
</template>

<script setup>
  import dayjs from 'dayjs'

  const nowDate = ref(Date.now())

  const time = computed(() => {
    return dayjs(nowDate.value).format('HH:mm:ss')
  })

  const date = computed(() => {
    return dayjs(nowDate.value).format('YYYY-MM-DD')
  })

  const week = computed(() => {
    const day = dayjs(nowDate.value).day()
    const week = ['日', '一', '二', '三', '四', '五', '六']
    return '星期' + week[day]
  })

  let timer = null

  onMounted(() => {
    timer = setInterval(() => {
      nowDate.value = Date.now()
    }, 1000)
  })

  onUnmounted(() => {
    clearInterval(timer)
    timer = null
  })
</script>

<style lang="scss" scoped>
  .timeBox {
    display: flex;
    position: absolute;
    top: 42px;
    left: 16px;
    align-items: center;
    gap: 6px;
    color: #fff;
    font-size: 16px;
    line-height: 24px;
  }
</style>
