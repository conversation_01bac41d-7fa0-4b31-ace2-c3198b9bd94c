<template>
  <div class="pay">
    <LargeTitle title-text="薪酬分布" />
    <div class="chartBox">
      <VChart :option />
    </div>
  </div>
</template>

<script setup>
  import LargeTitle from './large-title.vue'

  const props = defineProps({
    chartData: {
      type: Array,
      default: () => [],
    },
  })

  const option = computed(() => {
    return {
      grid: {
        top: 26,
        left: 0,
        containLabel: true,
        bottom: 40,
        right: 0,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(11, 7, 1, 0.70)',
        borderColor: 'transparent',
        textStyle: {
          color: '#fff',
        },
      },
      xAxis: {
        type: 'category',
        data: props.chartData.map((item) => item.name),
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: 'rgba(72, 72, 73, 1)',
            type: 'dashed',
          },
        },
      },
      dataZoom: {
        bottom: 0,
        left: 0,
        right: 10,
        brushSelect: false,
        borderColor: 'transparent',
        backgroundColor: 'rgba(13, 51, 80, 0.3)',
        fillerColor: 'rgba(0, 193, 255, 0.2)',
        dataBackground: {
          lineStyle: {
            color: '#00C1FF',
          },
          areaStyle: {
            color: 'transparent',
          },
        },
        selectedDataBackground: {
          lineStyle: {
            color: '#00C1FF',
          },
          areaStyle: {
            color: 'transparent',
          },
        },
        handleStyle: {
          borderColor: '#00C1FF',
        },
      },
      series: [
        {
          type: 'line',
          name: '人数',
          showSymbol: false,
          data: props.chartData,
          smooth: true,
          lineStyle: {
            color: 'rgba(0, 193, 255, 1)',
          },
          itemStyle: {
            color: 'rgba(0, 193, 255, 1)',
          },
          emphasis: {
            focus: 'series',
          },
          areaStyle: {
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0, 193, 255, 0)',
                },
                {
                  offset: 1,
                  color: 'rgba(0, 193, 255, 0.2)',
                },
              ],
            },
          },
        },
      ],
    }
  })
</script>

<style lang="scss" scoped>
  .pay {
    // position: absolute;
    // top: 674px;
    height: 308px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 38px;
      padding: 0 18px;
      background-image: url('@/assets/images/large_screen/long-title.png');
      background-size: cover;

      .text {
        color: #fff;
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
      }

      .after {
        display: flex;
        align-items: center;
        gap: 4px;

        .dot {
          width: 6px;
          height: 2px;
        }
      }
    }

    .chartBox {
      width: 100%;
      height: 254px;
      margin-top: 16px;
    }
  }
</style>
