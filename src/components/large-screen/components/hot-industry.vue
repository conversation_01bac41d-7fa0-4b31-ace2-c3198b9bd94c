<template>
  <div class="hotIndustry">
    <LargeTitle title-text="热门行业" />
    <div class="list">
      <TransitionGroup>
        <div
          v-for="(item, index) in chartData"
          :key="item.name"
          class="item">
          <div class="itemText">
            <div class="rank">NO.{{ index + 1 }}</div>
            <div class="name">{{ item.name }}</div>
            <i
              v-show="index < 3"
              class="iconfont icon-dp_hot text-[14px] text-[rgba(253,60,49,1)]" />
          </div>
          <IndustryProgress :percent="item.value" />
        </div>
      </TransitionGroup>
    </div>
  </div>
</template>

<script setup>
  import IndustryProgress from './industry-progress.vue'
  import LargeTitle from './large-title.vue'

  defineProps({
    chartData: {
      type: Array,
      default: () => [],
    },
  })
</script>

<style lang="scss" scoped>
  .hotIndustry {
    display: flex;

    // position: absolute;
    // top: 290px;
    flex: 1;
    flex-direction: column;
    overflow: hidden;

    .list {
      flex: 1;
      margin-top: 16px;
      overflow: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      .item {
        .itemText {
          display: flex;
          align-content: center;

          .rank {
            color: rgb(255 255 255 / 45%);
            font-size: 14px;
            line-height: 22px;
          }

          .name {
            margin: 0 4px;
            color: #fff;
            font-size: 14px;
            line-height: 22px;
          }
        }

        & + .item {
          margin-top: 6px;
        }
      }
    }
  }

  .v-move {
    transition: all 0.3s ease-in-out;
  }
</style>
