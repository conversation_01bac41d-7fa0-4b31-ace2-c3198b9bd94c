<template>
  <div class="page-header">
    <ElPageHeader @back="handleBack">
      <template #title>
        <span class="text-[#909399]">{{ title }}</span>
      </template>
      <template #content>
        <span class="text-[20px] font-[600] text-[#303133]">{{ content }}</span>
      </template>
      <template #extra>
        <slot name="extra" />
      </template>
    </ElPageHeader>
  </div>
</template>

<script setup>
  const { onBack } = defineProps({
    title: {
      type: String,
      default: '返回',
    },
    content: {
      type: String,
      default: '',
    },
    onBack: {
      type: Function,
      default: undefined,
    },
  })

  const router = useRouter()
  function handleBack() {
    onBack ? onBack() : router.back()
  }
</script>

<style lang="scss" scoped></style>
