<template>
  <div class="back_title">
    <div
      class="flex cursor-pointer items-center"
      @click="onBack">
      <ElIcon><Back /></ElIcon>
      <span class="back">返回</span>
    </div>
    <ElDivider direction="vertical" />
    <span class="title">{{ title }}</span>
  </div>
</template>

<script setup>
  import { Back } from '@element-plus/icons-vue'

  const props = defineProps({
    title: {
      type: String,
      default: '查看详情',
    },
    isCustom: {
      type: Boolean,
      default: false,
    },
  })
  const emit = defineEmits(['on-back'])
  const { title, isCustom } = toRefs(props)
  const router = useRouter()
  const onBack = () => {
    isCustom.value ? emit('on-back') : router.back()
  }
</script>

<style lang="scss" scoped>
  .back_title {
    display: flex;
    align-items: center;
    height: 28px;

    .back {
      margin: 0 10px;
      color: #909399;
      font-size: 14px;
    }

    .title {
      margin: 0 10px;
      color: #303133;
      font-weight: bold;
      font-size: 20px;
    }
  }
</style>
