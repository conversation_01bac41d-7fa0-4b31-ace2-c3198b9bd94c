<template>
  <div class="page">
    <div
      class="page_header"
      :style="{
        borderBottom: headerLine ? '1px solid #dcdfe6' : 'none',
        justifyContent: slots['header-extra'] ? 'space-between' : 'flex-start',
      }">
      <slot name="header">
        <p class="page_header_title">{{ title }}</p>
      </slot>
      <slot name="header-extra" />
    </div>
    <div class="page_search">
      <slot name="search" />
    </div>
    <div
      ref="tableRef"
      class="page_table">
      <slot
        name="table"
        :max-height="tableMaxHeight" />
    </div>
    <div
      class="page_footer"
      :style="{
        justifyContent: slots['footer-extra'] ? 'space-between' : 'flex-end',
      }">
      <slot name="footer-extra"></slot>
      <slot name="pagination" />
    </div>
  </div>
</template>

<script setup>
  import { useElementSize } from '@vueuse/core'

  const { title, headerLine } = defineProps({
    title: {
      type: String,
      default: '',
    },
    headerLine: {
      type: Boolean,
      default: true,
    },
  })

  const slots = useSlots()

  const { height: tableMaxHeight } = useElementSize(useTemplateRef('tableRef'))
</script>

<style lang="scss" scoped>
  .page {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 0 20px;
    overflow: hidden;
    background-color: #fff;

    &_header {
      display: flex;
      align-items: center;
      height: 60px;
      &_title {
        color: #303133;
        font-weight: 600;
        font-size: 20px;
      }
    }

    &_search {
      padding: 20px 0;
    }

    &_table {
      flex: 1;
      margin-top: -18px;
      overflow: auto;
    }

    &_footer {
      display: flex;
      padding: 15px 0;
    }
  }
</style>
