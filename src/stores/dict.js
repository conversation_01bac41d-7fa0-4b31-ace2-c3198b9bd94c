// store/modules/dict.js
import { getDictByKey } from '@/apis/common' // 导入统一的字典接口
import { isEmpty } from '@/utils/is'

export const useDictStore = defineStore('dict', {
  state: () => ({
    dict: new Map(), // 使用Map提高查找效率
    loading: new Set(), // 记录正在加载的字典类型
  }),

  getters: {
    // 获取所有字典
    getAllDict: (state) => {
      const result = {}
      for (const [key, value] of state.dict) {
        result[key] = value
      }
      return result
    },

    // 检查字典是否存在
    hasDict: (state) => (dictType) => {
      return state.dict.has(dictType)
    },

    // 检查字典是否正在加载
    isLoading: (state) => (dictType) => {
      return state.loading.has(dictType)
    },
  },

  actions: {
    // 获取字典数据
    getDict(dictType) {
      if (!dictType) {
        return null
      }
      return this.dict.get(dictType) || null
    },

    // 设置字典数据
    setDict(dictType, value) {
      if (dictType && value) {
        this.dict.set(dictType, value)
      }
    },

    // 删除指定字典
    removeDict(dictType) {
      if (dictType && this.dict.has(dictType)) {
        this.dict.delete(dictType)
        return true
      }
      return false
    },

    // 清空所有字典
    cleanDict() {
      this.dict.clear()
      this.loading.clear()
    },

    // 从服务器加载字典数据（单个字典类型）
    async loadDict(dictType) {
      if (!dictType || this.loading.has(dictType)) {
        return 'loading'
      }

      // 如果已经存在数据，直接返回
      const existingDict = this.getDict(dictType)
      if (existingDict && existingDict.length > 0) {
        return existingDict
      }

      this.loading.add(dictType)

      try {
        // 调用统一的字典接口
        const res = await getDictByKey(dictType)

        // 处理返回的数据结构，假设返回格式为 { data: { [dictType]: [...] } }
        const dictData = res[dictType] || []

        this.setDict(dictType, dictData)
        return dictData
      } catch (error) {
        console.error(`加载字典 ${dictType} 失败:`, error)
        throw error
      } finally {
        this.loading.delete(dictType)
      }
    },
  },
})

/**
 * 字典Hook函数
 * @param {...string} dictTypes 字典类型列表
 * @returns {Object} 响应式的字典数据对象
 */
export function useDict(...dictTypes) {
  const dictStore = useDictStore()
  const res = ref({})

  // 初始化所有字典类型的响应式数据
  dictTypes.forEach((dictType) => {
    res.value[dictType] = []
  })

  // 加载字典数据
  const loadDictData = async () => {
    // 循环加载每个字典类型
    for (const dictType of dictTypes) {
      try {
        // 从服务器加载数据
        const dictData = await dictStore.loadDict(dictType)

        // loading代表此type正在被加载
        if (dictData === 'loading') {
          // 监听store变化，等待数据加载完成
          const unsubscribe = dictStore.$onAction(
            ({
              name, // action 名称
              args,
              after, // 在 action 返回或解决后的钩子
            }) => {
              after(() => {
                if (name === 'setDict' && args[0] === dictType) {
                  const existingDict = dictStore.getDict(dictType)
                  if (existingDict && existingDict.length > 0) {
                    res.value[dictType] = existingDict
                    // 删除订阅
                    unsubscribe()
                  }
                }
              })
            },
          )
        } else if (!isEmpty(dictData)) {
          res.value[dictType] = dictData
        }
      } catch (error) {
        console.error(`加载字典 ${dictType} 失败:`, error)
        res.value[dictType] = []
      }
    }
  }

  // 立即加载数据
  loadDictData()

  return toRefs(res.value)
}

/**
 * 字典标签获取Hook
 * @param {string} dictType 字典类型
 * @param {*} value 值
 * @param {Object} options 配置选项
 * @param {string} options.labelKey 标签键名，默认'label'
 * @param {string} options.valueKey 值键名，默认'value'
 * @returns {ComputedRef} 根据值获取标签的计算属性
 */
export function useDictLabel(dictType, value, options = {}) {
  const { labelKey = 'label', valueKey = 'value' } = options

  // 使用useDict确保字典数据存在
  const dictData = useDict(dictType)

  // 返回计算属性
  return computed(() => {
    const data = dictData[dictType].value
    if (!data || !Array.isArray(data)) {
      return ''
    }

    const item = data.find((dict) => dict[valueKey] == value)
    return item ? item[labelKey] : ''
  })
}

/**
 * 字典值获取Hook
 * @param {string} dictType 字典类型
 * @param {*} label 标签
 * @param {Object} options 配置选项
 * @param {string} options.labelKey 标签键名，默认'label'
 * @param {string} options.valueKey 值键名，默认'value'
 * @returns {ComputedRef} 根据标签获取值的计算属性
 */
export function useDictValue(dictType, label, options = {}) {
  const { labelKey = 'label', valueKey = 'value' } = options

  // 使用useDict确保字典数据存在
  const dictData = useDict(dictType)

  // 返回计算属性
  return computed(() => {
    const data = dictData[dictType].value
    if (!data || !Array.isArray(data)) {
      return null
    }

    const item = data.find((dict) => dict[labelKey] === label)
    return item ? item[valueKey] : null
  })
}

/**
 * 字典选项获取Hook
 * @param {string} dictType 字典类型
 * @returns {ComputedRef} 字典选项数组
 */
export function useDictOptions(dictType) {
  // 使用useDict确保字典数据存在
  const dictData = useDict(dictType)

  return computed(() => dictData[dictType].value || [])
}

// 兼容性函数（保持原有API）
/**
 * 根据字典值获取标签
 * @param {string} dictType 字典类型
 * @param {string|number} value 字典值
 * @param {Object} options 配置选项
 * @returns {string} 字典标签
 */
export function getDictLabel(dictType, value, options = {}) {
  const dictStore = useDictStore()
  const { labelKey = 'label', valueKey = 'value' } = options

  const dictData = dictStore.getDict(dictType)

  if (!dictData || !Array.isArray(dictData)) {
    return ''
  }

  const item = dictData.find((dict) => dict[valueKey] == value)
  return item ? item[labelKey] : ''
}

/**
 * 根据字典标签获取值
 * @param {string} dictType 字典类型
 * @param {string} label 字典标签
 * @param {Object} options 配置选项
 * @returns {string|number} 字典值
 */
export function getDictValue(dictType, label, options = {}) {
  const dictStore = useDictStore()
  const { labelKey = 'label', valueKey = 'value' } = options

  const dictData = dictStore.getDict(dictType)

  if (!dictData || !Array.isArray(dictData)) {
    return null
  }

  const item = dictData.find((dict) => dict[labelKey] === label)
  return item ? item[valueKey] : null
}

/**
 * 获取字典选项（用于下拉框等组件）
 * @param {string} dictType 字典类型
 * @returns {Array} 字典选项数组
 */
export function getDictOptions(dictType) {
  const dictStore = useDictStore()
  return dictStore.getDict(dictType) || []
}

// 使用示例：

// 1. 基本使用（单个字典）
/*
<template>
  <div>
    <el-select v-model="userStatus">
      <el-option
        v-for="item in user_status"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </div>
</template>

<script setup>
import { useDict, useDictLabel } from '@/store/modules/dict'

// 使用字典
const { user_status } = useDict('user_status')
const userStatus = ref('')

// 使用Hook获取标签
const statusLabel = useDictLabel('user_status', userStatus)
</script>
*/

// 2. 多个字典使用
/*
<script setup>
import { useDict, useDictOptions } from '@/store/modules/dict'

// 使用多个字典（会循环单独加载每个字典）
const { user_status, company_types, area_options } = useDict(
  'user_status',
  'company_types',
  'area_options'
)

// 获取字典选项
const userStatusOptions = useDictOptions('user_status')
const companyTypeOptions = useDictOptions('company_types')
</script>
*/

// 3. 在组件中使用标签转换
/*
<template>
  <div>
    <span>{{ statusLabel }}</span>
  </div>
</template>

<script setup>
import { useDictLabel } from '@/store/modules/dict'

const userStatus = ref(1)
const statusLabel = useDictLabel('user_status', userStatus)
</script>
*/
