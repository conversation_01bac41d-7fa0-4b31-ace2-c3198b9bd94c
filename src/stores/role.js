import { getUserRole } from '@/apis/sso'
import { spaceId } from '@/utils/config.js'

export const useRoleListStore = defineStore('userRole', () => {
  const roleList = ref([])

  const userRole = ref([])

  async function getMyRole() {
    try {
      const {
        data: { data: res },
      } = await getUserRole({ spaceId })
      userRole.value = res
    } catch (error) {
      console.log(error)
    }
  }

  return {
    roleList,
    userRole,
    getMyRole,
  }
})
