import * as apis from '@/apis/dictionany'

export const useDictionaryStore = defineStore('dictionary', () => {
  const cityData = ref({})
  const getCityData = async () => {
    const res = await apis.getCityData()
    cityData.value = res.data.data
  }
  const nationData = ref({})
  const getNation = async () => {
    const res = await apis.getNation()
    nationData.value = res.data.data
  }
  const certificateDropdown = ref({})
  const getCertificateDropdown = async () => {
    const res = await apis.getCertificateDropdown()
    certificateDropdown.value = res.data.data
  }
  function init() {
    getCityData()
    getNation()
    getCertificateDropdown()
  }
  return { init, cityData, nationData, certificateDropdown }
})
