import process from 'node:process'
import { fileURLToPath, URL } from 'node:url'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import components from 'unplugin-vue-components/vite'
// 2在维护所以用这个插件
import { compression } from 'vite-plugin-compression2'

import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import { visualizer } from 'rollup-plugin-visualizer'
import { defineConfig, loadEnv } from 'vite'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())

  return {
    plugins: [
      vue(),
      vueDevTools(),
      tailwindcss(),
      // 自动导入api
      AutoImport({
        imports: [
          'vue',
          {
            'vue-router': [
              'useRouter',
              'useRoute',
              'createRouter',
              'createWebHistory',
            ],
          },
          'pinia',
        ],
        dts: true,
        eslintrc: {
          enabled: true, // Default `false`
          filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
          globalsPropValue: 'readonly',
        },
        resolvers: [
          // 两个地方都要设置importStyle: 'sass', 不然自定义的主题样式会被覆盖 因为
          // 因为有一些api调用的组件，例如message等
          ElementPlusResolver({
            importStyle: 'sass',
          }),
        ],
      }),
      components({
        resolvers: [
          // 自动按需导入组件和样式
          ElementPlusResolver({
            importStyle: 'sass',
          }),
        ],
      }),
      /**
       * 1、前端压缩好gzip，可以不需要Nginx实时压缩
       * 2、需要Nginx进一步配置才能真正生效（https://doc.ruoyi.vip/ruoyi-vue/other/faq.html#%E4%BD%BF%E7%94%A8gzip%E8%A7%A3%E5%8E%8B%E7%BC%A9%E9%9D%99%E6%80%81%E6%96%87%E4%BB%B6）
       */
      compression(),
      // 打包工具分析
      visualizer({
        filename: 'dist/stats.html',
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    css: {
      // 定义全局变量
      preprocessorOptions: {
        scss: {
          // 引入覆盖el默认变量得变量文件，并且使用
          // 您应该使用 @use 'xxx.scss' as *; 代替 @import 'xxx.scss';。因为 sass 团队说他们最终会删除 @import 语法。
          additionalData: `@use "@/assets/styles/element-var.scss" as *;`,
        },
      },
    },
    server: {
      host: '0.0.0.0',
      port: env.VITE_DEV_SERVER_PORT || 5173,
    },
    esbuild: {
      pure: ['console.log'],
      minify: true,
    },
  }
})
