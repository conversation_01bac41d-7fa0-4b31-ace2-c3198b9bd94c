{"explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"tsconfig.json": "tsconfig.*.json, env.d.ts", "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*", "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig, .lintstagedrc, .nvmrc, .npmrc", "project.yaml": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, .dockerignore"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit", "source.organizeImports": "explicit"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "eslint.enable": true, "eslint.validate": ["javascript", "javascriptreact", "vue"], "stylelint.enable": false, "cSpell.words": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Persistedstate", "sider"]}